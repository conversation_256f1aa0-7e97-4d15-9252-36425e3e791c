// 存储所有版本号
let allVersions = [];
let currentReleaseName = '';

// 添加缓存对象，用于存储已查询的Release信息
const releaseCache = {};

// 缓存branchMap的KEY数组
let cachedBranchMapKeys = [];

// 加载版本号列表
async function loadReleaseVersions() {
    try {
        const response = await fetch('http://10.169.128.35:8611/business/api/v1/tools/jira/getReleases', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                limit: 50,
                projectGroup: "TIC"
            })
        });

        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        const data = await response.json();
        
        allVersions = Array.isArray(data) ? data : (data.data || data.versions || []);
        
        if (!Array.isArray(allVersions)) {
            throw new Error('无效的数据格式');
        }

        updateVersionOptions(allVersions);
    } catch (error) {
        console.error('加载版本号列表失败：', error);
        showError('加载版本号列表失败: ' + error.message);
    }
}

// 更新版本号选项
function updateVersionOptions(versions) {
    const optionsContainer = document.getElementById('versionOptions');
    optionsContainer.innerHTML = '';
    
    versions.forEach(version => {
        const versionName = version.name || version.versionName || version.version || version;
        const option = document.createElement('div');
        option.className = 'select-option';
        option.textContent = versionName;
        option.onclick = () => selectVersion(versionName);
        optionsContainer.appendChild(option);
    });
}

// 选择版本号
function selectVersion(version) {
    const searchInput = document.getElementById('versionSearch');
    searchInput.value = version;
    document.getElementById('versionOptions').style.display = 'none';
    currentReleaseName = version;
}

// 搜索过滤版本号
function filterVersions(searchText) {
    const filtered = allVersions.filter(version => {
        const versionName = version.name || version.versionName || version.version || version;
        return versionName.toLowerCase().includes(searchText.toLowerCase());
    });
    updateVersionOptions(filtered);
}

// 查询Release信息（修复：每次都重新查询，不使用缓存）
async function queryRelease(releaseName, forceRefresh = false) {
    const hasCache = !!releaseCache[releaseName];
    console.log(`queryRelease调用: releaseName=${releaseName}, forceRefresh=${forceRefresh}, hasCache=${hasCache}`);

    // 如果强制刷新或缓存中没有数据，则重新查询
    if (forceRefresh || !releaseCache[releaseName]) {
        try {
            console.log(`🔄 正在调用API查询Release信息: ${releaseName}`);
            const response = await fetch('http://10.169.128.35:8611/business/api/v1/tools/jira/getReleaseIssue', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    releaseName: releaseName
                })
            });

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            const data = await response.json();
            let result;

            // 处理新的数据结构，返回包含taskMap和branchMap的完整数据
            if (data && data.data) {
                if (data.data.taskMap) {
                    // 保留原始branchMap数据
                    result = {
                        data: data.data.taskMap,
                        branchMap: data.data.branchMap || {}
                    };
                } else {
                    result = data;
                }

                // 缓存branchMap的KEY数组
                if (data.data.branchMap) {
                    cachedBranchMapKeys = Object.keys(data.data.branchMap);
                    console.log('缓存branchMap keys:', cachedBranchMapKeys);
                } else {
                    cachedBranchMapKeys = [];
                }
            } else {
                result = data;
                cachedBranchMapKeys = [];
            }

            // 将结果存入缓存
            releaseCache[releaseName] = result;
            return result;
        } catch (error) {
            throw new Error(`查询失败: ${error.message}`);
        }
    } else {
        console.log(`使用缓存的Release信息: ${releaseName}`);
        return releaseCache[releaseName];
    }
}

// 查询历史Release版本信息
async function getChangeLog(releaseName) {
    try {
        console.log(`查询历史Release版本: ${releaseName}`);
        const response = await fetch('http://10.169.128.35:8611/business/api/v1/tools/jira/getChangeLog', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                releaseName: releaseName
            })
        });

        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        const data = await response.json();

        // 检查返回结果
        if (data.resultCode === "0" && data.data && Array.isArray(data.data)) {
            return data.data;
        } else {
            console.warn('历史版本查询返回异常:', data);
            return [];
        }
    } catch (error) {
        console.error('查询历史Release版本失败:', error);
        return [];
    }
}

// 查询分支检查信息
async function checkBranch(releaseName) {
    try {
        console.log(`查询分支检查信息: ${releaseName}`);
        const response = await fetch('http://10.169.128.35:8611/business/api/v1/release/release/checkBranch', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                releaseName: releaseName
            })
        });

        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        const data = await response.json();

        // 检查返回结果
        if (data.resultCode === "0" && data.data) {
            return data.data;
        } else {
            console.warn('分支检查查询返回异常:', data);
            return null;
        }
    } catch (error) {
        console.error('查询分支检查信息失败:', error);
        throw error;
    }
}

// 加载并显示历史版本信息
async function loadHistoryVersions(releaseName) {
    try {
        const loadingElement = document.getElementById('history-versions-loading');
        const contentElement = document.getElementById('history-versions-content');

        // 显示加载状态
        loadingElement.style.display = 'block';
        contentElement.innerHTML = '';

        // 获取历史版本数据
        const historyVersions = await getChangeLog(releaseName);

        // 生成历史版本HTML
        let html = '';

        if (historyVersions.length === 0) {
            html = '<div class="info">暂无历史版本信息</div>';
        } else {
            html = `
                <div class="history-versions-list">
                    ${historyVersions.map((version, index) => `
                        <div class="history-version-item">
                            <span class="version-index">${index + 1}</span>
                            <span class="version-name">${version}</span>
                        </div>
                    `).join('')}
                </div>
            `;
        }

        // 隐藏加载状态，显示内容
        loadingElement.style.display = 'none';
        contentElement.innerHTML = html;

        console.log(`已加载 ${historyVersions.length} 个历史版本`);
    } catch (error) {
        console.error('加载历史版本失败:', error);
        
        const loadingElement = document.getElementById('history-versions-loading');
        const contentElement = document.getElementById('history-versions-content');
        
        loadingElement.style.display = 'none';
        contentElement.innerHTML = '<div class="error">加载历史版本失败</div>';
    }
}

// 加载需求/功能Issue信息（修复：支持强制刷新）
async function loadRequirementsIssues(releaseName, forceRefresh = false) {
    try {
        console.log(`📋 loadRequirementsIssues调用: releaseName=${releaseName}, forceRefresh=${forceRefresh}`);
        const loadingElement = document.getElementById('requirements-loading');
        const contentElement = document.getElementById('requirements-content');

        // 显示加载状态
        loadingElement.style.display = 'block';
        loadingElement.textContent = '加载Issue信息中...';

        // 获取Release信息（强制刷新）
        const releaseData = await queryRelease(releaseName, forceRefresh);

        if (!releaseData) {
            throw new Error('未获取到Release数据');
        }

        if (!releaseData.data) {
            throw new Error('Release数据格式错误：缺少data字段');
        }

        const { data } = releaseData;
        console.log('获取到的数据结构:', data);

        // 生成Issue列表HTML
        let html = '';

        // 根据实际API返回的数据结构处理（data是taskMap）
        ['Story', 'Task', 'Bug'].forEach(type => {
            if (data[type] && Array.isArray(data[type]) && data[type].length > 0) {
                html += `
                    <div class="issue-section">
                        <h4>${type} (${data[type].length})</h4>
                        <table>
                            <thead>
                                <tr>
                                    <th>Issue</th>
                                    <th>Title</th>
                                    <th>Assignee</th>
                                    <th>Components</th>
                                    <th>Status</th>
                                </tr>
                            </thead>
                            <tbody>
                                ${data[type].map(item => {
                                    const issueKey = item.key || '-';
                                    const summary = item.summary || '-';
                                    const assignee = item.assignee || '-';
                                    const components = Array.isArray(item.components) ? item.components.join(', ') : '-';
                                    const status = item.status || '-';

                                    // 判断是否需要显示title（超出长度限制时）
                                    const issueTitle = issueKey.length > 20 ? issueKey : '';
                                    const assigneeTitle = assignee.length > 100 ? assignee : '';
                                    const componentsTitle = components.length > 300 ? components : '';
                                    const statusTitle = status.length > 20 ? status : '';

                                    return `
                                        <tr>
                                            <td${issueTitle ? ` title="${issueTitle}"` : ''}>${issueKey}</td>
                                            <td>
                                                <a href="https://jira.sgsonline.com.cn/browse/${item.key}" target="_blank" title="${summary}">
                                                    ${summary}
                                                </a>
                                            </td>
                                            <td${assigneeTitle ? ` title="${assigneeTitle}"` : ''}>${assignee}</td>
                                            <td${componentsTitle ? ` title="${componentsTitle}"` : ''}>${components}</td>
                                            <td${statusTitle ? ` title="${statusTitle}"` : ''}>${status}</td>
                                        </tr>
                                    `;
                                }).join('')}
                            </tbody>
                        </table>
                    </div>
                `;
            }
        });

        if (html === '') {
            // 检查是否有数据但格式不匹配
            const dataKeys = Object.keys(data);
            console.log('数据中的keys:', dataKeys);

            if (dataKeys.length > 0) {
                html = `<div class="info">暂无Issue信息<br>数据结构: ${dataKeys.join(', ')}</div>`;
            } else {
                html = '<div class="info">暂无Issue信息</div>';
            }
        }

        // 隐藏加载状态，显示内容
        loadingElement.style.display = 'none';
        contentElement.innerHTML = html;

        console.log('已加载需求/功能Issue信息');
    } catch (error) {
        console.error('❌ 加载需求/功能Issue失败:', error);
        console.error('错误详情:', error.stack);

        const loadingElement = document.getElementById('requirements-loading');
        const contentElement = document.getElementById('requirements-content');

        if (loadingElement) loadingElement.style.display = 'none';
        if (contentElement) {
            contentElement.innerHTML = `
                <div class="error">
                    <strong>加载Issue信息失败</strong><br>
                    错误信息: ${error.message}<br>
                    版本: ${releaseName}<br>
                    强制刷新: ${forceRefresh}
                </div>
            `;
        }

        // 重新抛出错误，让上层调用者知道
        throw error;
    }
}

// 显示错误信息
function showError(message) {
    console.error(message);
    // 可以在这里添加更多的错误显示逻辑
}

// 清除缓存
function clearReleaseCache(releaseName) {
    console.log(`🗑️ 清除缓存: ${releaseName}`);
    if (releaseName && releaseCache[releaseName]) {
        delete releaseCache[releaseName];
        console.log(`✓ 已删除 ${releaseName} 的缓存`);
    } else {
        console.log(`ℹ️ ${releaseName} 没有缓存数据`);
    }
    // 同时清除branchMap keys缓存
    cachedBranchMapKeys = [];
    console.log(`✓ 已清除branchMap keys缓存`);
}

// 获取缓存的branchMap keys
function getCachedBranchMapKeys() {
    return [...cachedBranchMapKeys]; // 返回副本，避免外部修改
}

// 调试函数：在控制台显示当前缓存的branchMap keys
function debugBranchMapKeys() {
    console.log('当前缓存的branchMap keys:', cachedBranchMapKeys);
    return cachedBranchMapKeys;
}

// 将调试函数暴露到全局，方便在浏览器控制台中调用
window.debugBranchMapKeys = debugBranchMapKeys;
window.getCachedBranchMapKeys = getCachedBranchMapKeys;

// 其他Tab页面的占位函数
async function loadCodeScanInfo(releaseName) {
    const contentElement = document.getElementById('code-scan-content');
    if (contentElement) {
        contentElement.innerHTML = '<div class="info">代码扫描功能开发中...</div>';
    }
}

async function loadTestDeployInfo(releaseName) {
    const contentElement = document.getElementById('test-deploy-content');
    if (contentElement) {
        contentElement.innerHTML = '<div class="info">Test环境发布功能开发中...</div>';
    }
}

async function loadUatDeployInfo(releaseName) {
    const contentElement = document.getElementById('uat-deploy-content');
    if (contentElement) {
        contentElement.innerHTML = '<div class="info">UAT环境发布功能开发中...</div>';
    }
}

async function loadProdDeployInfo(releaseName) {
    const contentElement = document.getElementById('prod-deploy-content');
    if (contentElement) {
        contentElement.innerHTML = '<div class="info">上线发布功能开发中...</div>';
    }
}

async function loadReleaseCheckInfo(releaseName) {
    const contentElement = document.getElementById('release-check-content');
    if (contentElement) {
        contentElement.innerHTML = '<div class="info">发布检查功能开发中...</div>';
    }
}

// 初始化Tab切换功能（修复：每次切换都重新加载数据）
function initTabs() {
    const tabs = document.querySelectorAll('.tab');
    const tabContents = document.querySelectorAll('.tab-content');

    tabs.forEach(tab => {
        tab.addEventListener('click', () => {
            // 移除所有active类
            tabs.forEach(t => t.classList.remove('active'));
            tabContents.forEach(content => content.classList.remove('active'));

            // 添加active类到当前tab
            tab.classList.add('active');

            // 显示对应的内容
            const tabId = tab.getAttribute('data-tab');
            const targetContent = document.getElementById(`${tabId}-content`);
            if (targetContent) {
                targetContent.classList.add('active');
            }

            // 每次切换tab都重新加载数据（修复问题1）
            if (currentReleaseName) {
                console.log(`切换到${tabId} tab，重新加载数据`);
                switch(tabId) {
                    case 'requirements':
                        loadRequirementsIssues(currentReleaseName, true); // 强制刷新
                        break;
                    case 'development':
                        loadDevelopmentInfo(currentReleaseName, true); // 强制刷新
                        break;
                    case 'code-scan':
                        loadCodeScanInfo(currentReleaseName);
                        break;
                    case 'test-deploy':
                        loadTestDeployInfo(currentReleaseName);
                        break;
                    case 'uat-deploy':
                        loadUatDeployInfo(currentReleaseName);
                        break;
                    case 'prod-deploy':
                        loadProdDeployInfo(currentReleaseName);
                        break;
                    case 'release-check':
                        loadReleaseCheckInfo(currentReleaseName);
                        break;
                }
            } else {
                // 如果没有选择版本，显示提示
                if (targetContent) {
                    targetContent.innerHTML = '<div class="loading">请先选择一个版本号</div>';
                }
            }
        });
    });
}

// 加载开发信息（分支检查）（修复：支持强制刷新）
async function loadDevelopmentInfo(releaseName, forceRefresh = false) {
    try {
        console.log(`🔧 loadDevelopmentInfo调用: releaseName=${releaseName}, forceRefresh=${forceRefresh}`);
        const loadingElement = document.getElementById('development-loading');
        const contentElement = document.getElementById('development-content');

        // 显示加载状态
        loadingElement.style.display = 'block';
        loadingElement.textContent = '加载分支检查信息中...';

        // 获取分支检查信息
        const branchData = await checkBranch(releaseName);

        if (!branchData || !branchData.projectMap) {
            throw new Error('未找到分支检查信息');
        }

        const { projectMap } = branchData;

        // 生成分支检查HTML
        let html = `
            <div class="branch-check-section">
                <h4>分支检查结果</h4>
        `;

        // 遍历项目映射
        Object.entries(projectMap).forEach(([projectName, branches]) => {
            html += `
                <div class="project-container">
                    <h5>项目: ${projectName}</h5>
            `;

            if (branches && branches.length > 0) {
                // 有分支列表，展示分支列表
                html += `
                    <table class="branch-table">
                        <thead>
                            <tr>
                                <th class="branch-name-col">分支名称</th>
                                <th class="branch-status-col">状态</th>
                            </tr>
                        </thead>
                        <tbody>
                `;

                branches.forEach(branch => {
                    html += `
                        <tr>
                            <td class="branch-name-col" title="${branch.length > 50 ? branch : ''}">${branch}</td>
                            <td class="branch-status-col">
                                <span class="status-success">✓ 分支存在</span>
                            </td>
                        </tr>
                    `;
                });

                html += `
                        </tbody>
                    </table>
                `;
            } else {
                // 分支为空，提示分支不存在
                html += `
                    <div class="no-branches-warning">
                        <div class="warning-icon">⚠️</div>
                        <div class="warning-text">
                            <strong>分支不存在，需检查</strong>
                            <p>该项目下未找到对应的分支信息，请检查分支是否已创建。</p>
                        </div>
                    </div>
                `;
            }

            html += `</div>`;
        });

        html += `</div>`;

        // 隐藏加载状态，显示内容
        loadingElement.style.display = 'none';
        contentElement.innerHTML = html;

        console.log('已加载分支检查信息');
    } catch (error) {
        console.error('加载分支检查信息失败:', error);

        const loadingElement = document.getElementById('development-loading');
        const contentElement = document.getElementById('development-content');

        loadingElement.style.display = 'none';
        contentElement.innerHTML = '<div class="error">加载分支检查信息失败: ' + error.message + '</div>';
    }
}

// 更新流程进度
function updateFlowProgress(currentStep) {
    const steps = document.querySelectorAll('.flow-step');
    const progressBar = document.getElementById('flowProgress');

    steps.forEach((step, index) => {
        const circle = step.querySelector('.step-circle');
        const stepNumber = index + 1;

        if (stepNumber < currentStep) {
            circle.classList.remove('active');
            circle.classList.add('completed');
        } else if (stepNumber === currentStep) {
            circle.classList.remove('completed');
            circle.classList.add('active');
        } else {
            circle.classList.remove('active', 'completed');
        }
    });

    // 更新进度条
    const progressPercentage = ((currentStep - 1) / (steps.length - 1)) * 100;
    progressBar.style.width = `${Math.max(0, Math.min(100, progressPercentage))}%`;
}

// 初始化事件监听
document.addEventListener('DOMContentLoaded', () => {
    loadReleaseVersions();

    const searchInput = document.getElementById('versionSearch');
    const optionsContainer = document.getElementById('versionOptions');

    // 搜索输入事件
    searchInput.addEventListener('input', (e) => {
        const searchText = e.target.value;
        filterVersions(searchText);
        optionsContainer.style.display = 'block';
    });

    searchInput.addEventListener('focus', () => {
        optionsContainer.style.display = 'block';
    });

    // 点击其他地方隐藏选项
    document.addEventListener('click', (e) => {
        if (!e.target.closest('.select-container')) {
            optionsContainer.style.display = 'none';
        }
    });

    // 表单提交事件（修复：每次点击查询都强制刷新）
    document.getElementById('releaseForm').addEventListener('submit', async (event) => {
        event.preventDefault();

        const version = document.getElementById('versionSearch').value;

        if (!version) {
            alert('请选择版本号');
            return;
        }

        // 检查是否切换了版本
        const isVersionChanged = currentReleaseName !== version;
        const oldVersion = currentReleaseName;
        currentReleaseName = version;

        try {
            // 每次点击查询都强制刷新，不管版本是否变化
            console.log(`🔍 点击查询按钮: ${version}${isVersionChanged ? ` (从 ${oldVersion} 切换)` : ' (重新查询)'}`);

            // 强制清除所有缓存
            console.log(`🗑️ 强制清除所有缓存`);
            Object.keys(releaseCache).forEach(key => {
                delete releaseCache[key];
            });
            cachedBranchMapKeys = [];

            // 加载历史版本信息
            loadHistoryVersions(version);

            // 强制刷新当前激活tab的内容
            const activeTab = document.querySelector('.tab.active');
            if (activeTab) {
                const tabId = activeTab.getAttribute('data-tab');
                console.log(`📋 当前激活tab: ${tabId}，强制刷新数据`);
                switch(tabId) {
                    case 'requirements':
                        await loadRequirementsIssues(version, true); // 强制刷新
                        break;
                    case 'development':
                        await loadDevelopmentInfo(version, true); // 强制刷新
                        break;
                    case 'code-scan':
                        loadCodeScanInfo(version);
                        break;
                    case 'test-deploy':
                        loadTestDeployInfo(version);
                        break;
                    case 'uat-deploy':
                        loadUatDeployInfo(version);
                        break;
                    case 'prod-deploy':
                        loadProdDeployInfo(version);
                        break;
                    case 'release-check':
                        loadReleaseCheckInfo(version);
                        break;
                }
            }

            // 更新流程进度（这里可以根据实际业务逻辑来确定当前步骤）
            updateFlowProgress(3); // 假设当前在"开发中"步骤

            console.log(`✅ 查询完成: ${version}`);

        } catch (error) {
            console.error('❌ 查询失败:', error);
            console.error('错误详情:', error.stack);

            // 显示更详细的错误信息
            const errorMsg = `查询失败: ${error.message}\n版本: ${version}\n请检查网络连接和版本号是否正确`;
            alert(errorMsg);
        }
    });

    // 初始化Tab功能
    initTabs();

    // 默认更新流程进度
    updateFlowProgress(1);
});
