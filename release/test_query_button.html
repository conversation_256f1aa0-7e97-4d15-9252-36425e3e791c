<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <title>查询按钮测试</title>
    <style>
        body {
            margin: 20px;
            font-family: Arial, sans-serif;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 4px;
            background: #f9f9f9;
        }
        .test-button {
            padding: 10px 20px;
            margin: 5px;
            background: #4CAF50;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        .test-button:hover {
            background: #45a049;
        }
        .open-button {
            padding: 15px 30px;
            background: #2196F3;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
        }
        .open-button:hover {
            background: #1976D2;
        }
        .instructions {
            background: #e3f2fd;
            padding: 15px;
            border-radius: 4px;
            margin: 15px 0;
        }
        .step {
            margin: 10px 0;
            padding: 10px;
            background: white;
            border-left: 4px solid #4CAF50;
            border-radius: 0 4px 4px 0;
        }
        .expected {
            background: #e8f5e8;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .warning {
            background: #fff3cd;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
            border-left: 4px solid #ffc107;
        }
    </style>
</head>
<body>
    <h1>查询按钮刷新测试</h1>
    
    <div class="test-section">
        <h3>🎯 测试目标</h3>
        <p>验证每次点击查询按钮都会调用接口刷新数据，不管版本是否变化。</p>
    </div>

    <div class="test-section">
        <h3>📋 测试步骤</h3>
        <div class="instructions">
            <strong>请按照以下步骤进行测试：</strong>
            
            <div class="step">
                <strong>步骤1：</strong> 点击下面的按钮打开Release发布流程页面
            </div>
            
            <div class="step">
                <strong>步骤2：</strong> 按F12打开浏览器开发者工具，切换到Console（控制台）标签
            </div>
            
            <div class="step">
                <strong>步骤3：</strong> 在页面中选择一个Release版本号（如：TIC生产发布-250812）
            </div>
            
            <div class="step">
                <strong>步骤4：</strong> 点击"查询"按钮，观察控制台日志
            </div>
            
            <div class="step">
                <strong>步骤5：</strong> 不要切换版本，再次点击"查询"按钮，观察控制台日志
            </div>
            
            <div class="step">
                <strong>步骤6：</strong> 切换到另一个版本号，点击"查询"按钮，观察控制台日志
            </div>
            
            <div class="step">
                <strong>步骤7：</strong> 切换不同的Tab页面，观察是否有相应的加载日志
            </div>
        </div>
    </div>

    <div class="test-section">
        <h3>✅ 期望结果</h3>
        <div class="expected">
            <strong>每次点击查询按钮时，控制台应该显示：</strong>
            <ul>
                <li>🔍 点击查询按钮: [版本号] (重新查询 或 从 [旧版本] 切换)</li>
                <li>🗑️ 强制清除所有缓存</li>
                <li>📋 当前激活tab: [tab名称]，强制刷新数据</li>
                <li>📋 loadRequirementsIssues调用: releaseName=[版本号], forceRefresh=true</li>
                <li>🔄 正在调用API查询Release信息: [版本号]</li>
                <li>缓存branchMap keys: [数组内容]</li>
                <li>✅ 查询完成: [版本号]</li>
            </ul>
        </div>
        
        <div class="warning">
            <strong>⚠️ 如果没有看到这些日志，说明修复还有问题</strong>
        </div>
    </div>

    <div class="test-section">
        <h3>🚀 开始测试</h3>
        <button class="open-button" onclick="openTestPage()">打开Release发布流程页面进行测试</button>
    </div>

    <div class="test-section">
        <h3>🔧 调试工具</h3>
        <p>如果需要调试，可以在控制台中使用以下命令：</p>
        <div style="background: #f5f5f5; padding: 10px; border-radius: 4px; font-family: monospace;">
            // 查看当前缓存的branchMap keys<br>
            debugBranchMapKeys()<br><br>
            
            // 获取缓存数据<br>
            getCachedBranchMapKeys()<br><br>
            
            // 查看当前版本<br>
            console.log('当前版本:', currentReleaseName)
        </div>
    </div>

    <div class="test-section">
        <h3>📊 API测试</h3>
        <p>也可以直接测试API调用：</p>
        <input type="text" id="test-version" placeholder="输入版本号" value="TIC生产发布-250812" style="padding: 8px; width: 200px; margin-right: 10px;">
        <button class="test-button" onclick="testAPI()">测试API调用</button>
        <div id="api-result" style="margin-top: 10px; padding: 10px; background: #f5f5f5; border-radius: 4px; font-family: monospace; font-size: 12px;"></div>
    </div>

    <script>
        function openTestPage() {
            window.open('./releaseProcess.html', '_blank');
        }

        async function testAPI() {
            const version = document.getElementById('test-version').value;
            const resultDiv = document.getElementById('api-result');
            
            if (!version) {
                resultDiv.innerHTML = '<span style="color: red;">请输入版本号</span>';
                return;
            }
            
            resultDiv.innerHTML = '正在调用API...';
            
            try {
                const response = await fetch('http://*************:8611/business/api/v1/tools/jira/getReleaseIssue', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        releaseName: version
                    })
                });

                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }

                const data = await response.json();
                
                let result = `<span style="color: green;">✅ API调用成功</span><br>`;
                result += `版本: ${version}<br>`;
                result += `返回码: ${data.resultCode}<br>`;
                result += `消息: ${data.resultMsg}<br>`;
                
                if (data.data && data.data.branchMap) {
                    const keys = Object.keys(data.data.branchMap);
                    result += `branchMap keys: [${keys.join(', ')}]<br>`;
                    result += `branchMap内容:<br>${JSON.stringify(data.data.branchMap, null, 2)}`;
                } else {
                    result += '<span style="color: orange;">⚠️ 未找到branchMap数据</span>';
                }
                
                resultDiv.innerHTML = result;
            } catch (error) {
                resultDiv.innerHTML = `<span style="color: red;">❌ API调用失败: ${error.message}</span>`;
            }
        }
    </script>
</body>
</html>
