<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <title>优化后的Release查询测试</title>
    <style>
        body {
            margin: 0;
            padding: 15px;
            font-family: Arial, sans-serif;
            font-size: 14px;
        }

        .release-section {
            margin-bottom: 30px;
            border: 1px solid #ddd;
            border-radius: 6px;
            overflow: hidden;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        
        .section-title {
            margin: 0;
            padding: 15px;
            background-color: #f5f5f5;
            border-bottom: 1px solid #ddd;
            font-size: 18px;
            color: #333;
        }

        .release-info {
            display: flex;
            padding: 15px;
            gap: 20px;
        }
        
        .issues-container {
            flex: 1;
            min-width: 0;
            min-height: 60px;
            transition: all 0.3s ease;
        }
        
        .git-repos-section {
            width: 300px;
            background: #f9f9f9;
            border-radius: 4px;
            padding: 15px;
            border: 1px solid #eee;
            min-height: 60px;
            transition: all 0.3s ease;
        }

        .history-versions-container {
            padding: 15px;
            min-height: 60px;
            transition: all 0.3s ease;
        }
        
        .loading {
            text-align: center;
            padding: 20px;
            color: #666;
            background: #f9f9f9;
            border-radius: 4px;
            margin: 10px 0;
            transition: opacity 0.3s ease;
        }
        
        .loading::after {
            content: '';
            display: inline-block;
            width: 16px;
            height: 16px;
            margin-left: 8px;
            border: 2px solid #ddd;
            border-top: 2px solid #4CAF50;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .content-area {
            opacity: 0;
            transition: opacity 0.3s ease;
        }
        
        .content-area.show {
            opacity: 1;
        }
        
        .history-versions-list {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 10px;
            margin-top: 10px;
        }
        
        .history-version-item {
            display: flex;
            align-items: center;
            padding: 12px 15px;
            background: #f9f9f9;
            border: 1px solid #eee;
            border-radius: 4px;
            transition: background-color 0.2s;
        }
        
        .history-version-item:hover {
            background-color: #f0f0f0;
        }
        
        .version-index {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            width: 24px;
            height: 24px;
            background: #4CAF50;
            color: white;
            border-radius: 50%;
            font-size: 12px;
            font-weight: bold;
            margin-right: 12px;
            flex-shrink: 0;
        }
        
        .version-name {
            flex: 1;
            font-size: 14px;
            color: #333;
            word-break: break-word;
        }

        .demo-button {
            padding: 10px 20px;
            background: #4CAF50;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin: 10px 5px;
        }

        .demo-button:hover {
            background: #45a049;
        }

        /* 响应式布局优化 */
        @media (max-width: 768px) {
            .release-info {
                flex-direction: column;
            }
            
            .git-repos-section {
                width: 100%;
                margin-top: 20px;
            }
            
            .history-versions-list {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <h2>优化后的Release查询 - 演示页面</h2>
    
    <div style="margin-bottom: 20px;">
        <button class="demo-button" onclick="simulateQuery()">模拟查询</button>
        <button class="demo-button" onclick="resetDemo()">重置演示</button>
    </div>
    
    <!-- 历史release版本区域 -->
    <div class="release-section">
        <h2 class="section-title">历史release版本</h2>
        <div class="history-versions-container">
            <div class="loading" id="history-loading">加载历史版本中...</div>
            <div class="content-area" id="history-content" style="display: none;">
                <!-- 历史版本内容 -->
            </div>
        </div>
    </div>

    <!-- 发布清单区域 -->
    <div class="release-section">
        <h2 class="section-title">发布清单</h2>
        <div class="release-info">
            <!-- 左侧Issue列表 -->
            <div class="issues-container">
                <div class="loading" id="issues-loading">加载Issue信息中...</div>
                <div class="content-area" id="issues-content" style="display: none;">
                    <!-- Issue内容 -->
                </div>
            </div>
            <!-- 右侧Git仓库信息 -->
            <div class="git-repos-section">
                <h3>涉及Git仓库工程</h3>
                <div class="loading" id="git-loading">加载Git仓库信息中...</div>
                <div class="content-area" id="git-content" style="display: none;">
                    <!-- Git仓库内容 -->
                </div>
            </div>
        </div>
    </div>

    <!-- 发布检查清单区域 -->
    <div class="release-section">
        <h2 class="section-title">发布检查清单</h2>
        <div style="padding: 20px; text-align: center; color: #666;">
            发布检查清单内容区域（立即显示）
        </div>
    </div>

    <script>
        function simulateQuery() {
            // 重置所有状态
            resetDemo();
            
            // 模拟异步加载历史版本
            setTimeout(() => {
                loadHistoryVersions();
            }, 500);
            
            // 模拟异步加载Issue信息
            setTimeout(() => {
                loadIssuesContent();
            }, 1000);
            
            // 模拟异步加载Git仓库信息
            setTimeout(() => {
                loadGitContent();
            }, 1500);
        }
        
        function resetDemo() {
            // 重置所有加载状态
            document.getElementById('history-loading').style.display = 'block';
            document.getElementById('history-loading').style.opacity = '1';
            document.getElementById('history-content').style.display = 'none';
            document.getElementById('history-content').classList.remove('show');
            
            document.getElementById('issues-loading').style.display = 'block';
            document.getElementById('issues-loading').style.opacity = '1';
            document.getElementById('issues-content').style.display = 'none';
            document.getElementById('issues-content').classList.remove('show');
            
            document.getElementById('git-loading').style.display = 'block';
            document.getElementById('git-loading').style.opacity = '1';
            document.getElementById('git-content').style.display = 'none';
            document.getElementById('git-content').classList.remove('show');
        }
        
        function loadHistoryVersions() {
            const loading = document.getElementById('history-loading');
            const content = document.getElementById('history-content');
            
            const html = `
                <div class="history-versions-list">
                    <div class="history-version-item">
                        <span class="version-index">1</span>
                        <span class="version-name">TIC生产发布-250724（智能客服上线）</span>
                    </div>
                    <div class="history-version-item">
                        <span class="version-index">2</span>
                        <span class="version-name">智能客服上线</span>
                    </div>
                    <div class="history-version-item">
                        <span class="version-index">3</span>
                        <span class="version-name">172WK2待排</span>
                    </div>
                    <div class="history-version-item">
                        <span class="version-index">4</span>
                        <span class="version-name">TIC生产发布-250612</span>
                    </div>
                </div>
            `;
            
            loading.style.opacity = '0';
            setTimeout(() => {
                loading.style.display = 'none';
                content.innerHTML = html;
                content.style.display = 'block';
                setTimeout(() => {
                    content.classList.add('show');
                }, 10);
            }, 300);
        }
        
        function loadIssuesContent() {
            const loading = document.getElementById('issues-loading');
            const content = document.getElementById('issues-content');
            
            const html = `
                <div style="padding: 20px; background: #f9f9f9; border-radius: 4px;">
                    <h4>Story (3)</h4>
                    <p>• TIC-12345: 智能客服功能开发</p>
                    <p>• TIC-12346: 用户界面优化</p>
                    <p>• TIC-12347: 性能提升</p>
                </div>
            `;
            
            loading.style.opacity = '0';
            setTimeout(() => {
                loading.style.display = 'none';
                content.innerHTML = html;
                content.style.display = 'block';
                setTimeout(() => {
                    content.classList.add('show');
                }, 10);
            }, 300);
        }
        
        function loadGitContent() {
            const loading = document.getElementById('git-loading');
            const content = document.getElementById('git-content');
            
            const html = `
                <div>
                    <h4>前端应用</h4>
                    <ul>
                        <li>tic-web-portal</li>
                        <li>tic-admin-console</li>
                    </ul>
                    <h4>后端服务</h4>
                    <ul>
                        <li>tic-api-gateway</li>
                        <li>tic-user-service</li>
                    </ul>
                </div>
            `;
            
            loading.style.opacity = '0';
            setTimeout(() => {
                loading.style.display = 'none';
                content.innerHTML = html;
                content.style.display = 'block';
                setTimeout(() => {
                    content.classList.add('show');
                }, 10);
            }, 300);
        }
        
        // 页面加载时显示初始状态
        document.addEventListener('DOMContentLoaded', () => {
            resetDemo();
        });
    </script>
</body>
</html>
