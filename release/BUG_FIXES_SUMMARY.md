# Bug修复总结

## 修复的问题

### 问题1: Tab切换时不重新调用请求
**问题描述：** 切换Tab页时只调用一次请求，后续切换不再调用

**修复方案：**
- 修改 `initTabs()` 函数，每次Tab切换都强制重新加载数据
- 为所有Tab加载函数添加 `forceRefresh` 参数
- 在Tab切换事件中传递 `forceRefresh = true`

**修复代码：**
```javascript
// 每次切换tab都重新加载数据（修复问题1）
if (currentReleaseName) {
    console.log(`切换到${tabId} tab，重新加载数据`);
    switch(tabId) {
        case 'requirements':
            loadRequirementsIssues(currentReleaseName, true); // 强制刷新
            break;
        case 'development':
            loadDevelopmentInfo(currentReleaseName, true); // 强制刷新
            break;
        // ... 其他Tab
    }
}
```

### 问题2: 切换版本后查询不会重新调用接口
**问题描述：** 选择不同release版本后点击查询，不会调用 `getReleaseIssue` 接口，导致数据不刷新

**修复方案：**
- 在表单提交事件中检测版本是否发生变化
- 版本变化时清除缓存并强制刷新数据
- 修改 `queryRelease()` 函数支持强制刷新参数

**修复代码：**
```javascript
// 检查是否切换了版本（修复问题2）
const isVersionChanged = currentReleaseName !== version;
currentReleaseName = version;

// 如果版本发生变化，清除缓存
if (isVersionChanged) {
    console.log(`版本从 ${currentReleaseName} 切换到 ${version}，清除缓存`);
    clearReleaseCache(version);
}

// 强制刷新当前激活tab的内容
loadRequirementsIssues(version, true); // 强制刷新
loadDevelopmentInfo(version, true); // 强制刷新
```

### 问题3: branchMap keys缓存功能
**问题描述：** 需要将 `getReleaseIssue` 接口返回的 `branchMap` 中的KEY缓存为数组

**修复方案：**
- 添加全局变量 `cachedBranchMapKeys` 存储缓存数据
- 在 `queryRelease()` 函数中提取并缓存 branchMap 的 keys
- 提供 `getCachedBranchMapKeys()` 函数获取缓存数据
- 添加调试函数方便测试

**修复代码：**
```javascript
// 缓存branchMap的KEY数组
let cachedBranchMapKeys = [];

// 在queryRelease函数中缓存keys
if (data.data.branchMap) {
    cachedBranchMapKeys = Object.keys(data.data.branchMap);
    console.log('缓存branchMap keys:', cachedBranchMapKeys);
} else {
    cachedBranchMapKeys = [];
}

// 获取缓存的branchMap keys
function getCachedBranchMapKeys() {
    return [...cachedBranchMapKeys]; // 返回副本，避免外部修改
}
```

## 接口数据处理

### getReleaseIssue 接口返回示例：
```json
{
  "resultCode": "0",
  "resultMsg": "成功",
  "data": {
    "taskMap": {
      "Task": [...]
    },
    "branchMap": {
      "TIC Mall View": ["TIC-32453-release-250812"],
      "TIC Mall": ["TIC-32762-智能客服优化功能"]
    }
  }
}
```

### 缓存结果：
从上述 `branchMap` 中提取 keys：`["TIC Mall View", "TIC Mall"]`

## 新增功能

### 1. 强制刷新机制
- 所有数据加载函数支持 `forceRefresh` 参数
- Tab切换和版本切换时强制刷新数据
- 避免缓存导致的数据不更新问题

### 2. 版本变化检测
- 自动检测版本是否发生变化
- 版本变化时清除相关缓存
- 确保切换版本后数据正确更新

### 3. branchMap缓存系统
- 全局缓存 branchMap 的 keys
- 每次查询都根据接口返回刷新缓存
- 提供获取缓存数据的接口函数

### 4. 调试支持
- 添加调试函数 `debugBranchMapKeys()`
- 暴露到全局作用域，方便控制台调试
- 详细的控制台日志输出

## 测试方法

### 1. 使用测试页面
- `test_bug_fixes.html` - 完整的Bug修复测试
- 包含Tab切换、版本切换、缓存功能的测试

### 2. 浏览器控制台测试
```javascript
// 查看当前缓存的branchMap keys
debugBranchMapKeys();

// 获取缓存数据的副本
getCachedBranchMapKeys();
```

### 3. 实际功能测试
1. 打开 `releaseProcess.html`
2. 选择版本并查询
3. 切换不同Tab页面，观察是否重新加载
4. 切换不同版本，观察数据是否更新
5. 在控制台查看branchMap keys缓存

## 修改的文件

- `release/releaseProcess.js` - 主要修复文件
- `release/test_bug_fixes.html` - 新增测试页面

## 验证清单

- [x] Tab切换时重新调用API请求
- [x] 版本切换后重新调用getReleaseIssue接口
- [x] branchMap keys正确缓存为数组
- [x] 每次查询都刷新branchMap缓存
- [x] 提供调试和获取缓存数据的函数
- [x] 详细的控制台日志输出
- [x] 完整的测试页面验证

所有问题已修复完成，功能正常运行。
