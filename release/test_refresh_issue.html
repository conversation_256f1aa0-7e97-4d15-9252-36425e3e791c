<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <title>刷新问题测试</title>
    <style>
        body {
            margin: 20px;
            font-family: Arial, sans-serif;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        .test-button {
            padding: 10px 20px;
            margin: 5px;
            background: #4CAF50;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        .test-button:hover {
            background: #45a049;
        }
        .result {
            margin-top: 10px;
            padding: 10px;
            background: #f5f5f5;
            border-radius: 4px;
            max-height: 400px;
            overflow-y: auto;
        }
        .log {
            font-family: monospace;
            font-size: 12px;
            line-height: 1.4;
        }
        .api-call {
            color: #2196F3;
            font-weight: bold;
        }
        .success {
            color: #4CAF50;
        }
        .error {
            color: #f44336;
        }
    </style>
</head>
<body>
    <h1>Release刷新问题测试</h1>
    
    <div class="test-section">
        <h3>问题重现测试</h3>
        <p>测试切换release版本后是否会重新调用API</p>
        
        <div>
            <label>版本1: </label>
            <input type="text" id="version1" value="TIC生产发布-250812" style="padding: 8px; width: 200px;">
        </div>
        <div style="margin: 10px 0;">
            <label>版本2: </label>
            <input type="text" id="version2" value="TIC生产发布-250813" style="padding: 8px; width: 200px;">
        </div>
        
        <button class="test-button" onclick="testVersionSwitch()">测试版本切换刷新</button>
        <button class="test-button" onclick="clearLog()">清除日志</button>
        
        <div id="test-result" class="result log"></div>
    </div>

    <div class="test-section">
        <h3>直接API测试</h3>
        <p>直接测试getReleaseIssue接口调用</p>
        
        <input type="text" id="direct-version" value="TIC生产发布-250812" style="padding: 8px; width: 200px; margin-right: 10px;">
        <button class="test-button" onclick="testDirectAPI()">直接调用API</button>
        
        <div id="api-result" class="result log"></div>
    </div>

    <div class="test-section">
        <h3>打开实际页面</h3>
        <p>在实际页面中测试，请打开浏览器控制台查看日志</p>
        <button class="test-button" onclick="openRealPage()">打开Release发布流程页面</button>
        <div style="margin-top: 10px; padding: 10px; background: #e3f2fd; border-radius: 4px;">
            <strong>测试步骤：</strong>
            <ol>
                <li>打开页面后，按F12打开控制台</li>
                <li>选择一个版本号，点击查询</li>
                <li>观察控制台是否有 "🔄 正在调用API查询Release信息" 日志</li>
                <li>切换到另一个版本号，再次点击查询</li>
                <li>观察控制台是否再次出现API调用日志</li>
                <li>切换Tab页面，观察是否有相应的调用日志</li>
            </ol>
        </div>
    </div>

    <script>
        let logCount = 0;

        function log(message, type = 'info') {
            logCount++;
            const timestamp = new Date().toLocaleTimeString();
            const className = type === 'api' ? 'api-call' : (type === 'success' ? 'success' : (type === 'error' ? 'error' : ''));
            return `<div class="${className}">[${timestamp}] ${logCount}. ${message}</div>`;
        }

        function clearLog() {
            document.getElementById('test-result').innerHTML = '';
            document.getElementById('api-result').innerHTML = '';
            logCount = 0;
        }

        // 模拟版本切换测试
        async function testVersionSwitch() {
            const version1 = document.getElementById('version1').value;
            const version2 = document.getElementById('version2').value;
            const resultDiv = document.getElementById('test-result');
            
            resultDiv.innerHTML = log('开始版本切换测试...');
            
            // 模拟第一次查询
            resultDiv.innerHTML += log(`模拟查询版本: ${version1}`, 'info');
            try {
                const result1 = await callReleaseAPI(version1);
                resultDiv.innerHTML += log(`✓ 第一次查询成功`, 'success');
                resultDiv.innerHTML += log(`返回数据: ${JSON.stringify(result1).substring(0, 100)}...`, 'info');
            } catch (error) {
                resultDiv.innerHTML += log(`✗ 第一次查询失败: ${error.message}`, 'error');
            }
            
            // 等待一秒
            await new Promise(resolve => setTimeout(resolve, 1000));
            
            // 模拟切换版本
            resultDiv.innerHTML += log(`切换到版本: ${version2}`, 'info');
            try {
                const result2 = await callReleaseAPI(version2);
                resultDiv.innerHTML += log(`✓ 版本切换后查询成功`, 'success');
                resultDiv.innerHTML += log(`返回数据: ${JSON.stringify(result2).substring(0, 100)}...`, 'info');
            } catch (error) {
                resultDiv.innerHTML += log(`✗ 版本切换后查询失败: ${error.message}`, 'error');
            }
            
            resultDiv.innerHTML += log('版本切换测试完成', 'success');
        }

        // 直接API测试
        async function testDirectAPI() {
            const version = document.getElementById('direct-version').value;
            const resultDiv = document.getElementById('api-result');
            
            resultDiv.innerHTML = log(`直接调用API测试版本: ${version}`, 'api');
            
            try {
                const result = await callReleaseAPI(version);
                resultDiv.innerHTML += log(`✓ API调用成功`, 'success');
                resultDiv.innerHTML += log(`返回结果: ${JSON.stringify(result, null, 2)}`, 'info');
                
                // 检查branchMap
                if (result.data && result.data.branchMap) {
                    const keys = Object.keys(result.data.branchMap);
                    resultDiv.innerHTML += log(`branchMap keys: [${keys.join(', ')}]`, 'success');
                } else {
                    resultDiv.innerHTML += log('未找到branchMap数据', 'error');
                }
            } catch (error) {
                resultDiv.innerHTML += log(`✗ API调用失败: ${error.message}`, 'error');
            }
        }

        // 调用实际API
        async function callReleaseAPI(releaseName) {
            const response = await fetch('http://10.169.128.35:8611/business/api/v1/tools/jira/getReleaseIssue', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    releaseName: releaseName
                })
            });

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            return await response.json();
        }

        // 打开实际页面
        function openRealPage() {
            window.open('./releaseProcess.html', '_blank');
        }
    </script>
</body>
</html>
