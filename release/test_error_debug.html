<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <title>错误调试测试</title>
    <style>
        body {
            margin: 20px;
            font-family: Arial, sans-serif;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        .test-button {
            padding: 10px 20px;
            margin: 5px;
            background: #4CAF50;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        .test-button:hover {
            background: #45a049;
        }
        .result {
            margin-top: 10px;
            padding: 10px;
            background: #f5f5f5;
            border-radius: 4px;
            max-height: 400px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
        }
        .error {
            color: #f44336;
            background: #ffebee;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .success {
            color: #4CAF50;
            background: #e8f5e8;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .info {
            color: #2196F3;
            background: #e3f2fd;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <h1>切换版本错误调试</h1>
    
    <div class="test-section">
        <h3>🐛 问题重现</h3>
        <p>测试切换版本时的错误情况</p>
        
        <div>
            <label>版本1: </label>
            <input type="text" id="version1" value="TIC生产发布-250812" style="padding: 8px; width: 200px;">
        </div>
        <div style="margin: 10px 0;">
            <label>版本2: </label>
            <input type="text" id="version2" value="TIC生产发布-250813" style="padding: 8px; width: 200px;">
        </div>
        
        <button class="test-button" onclick="testVersionSwitchError()">重现版本切换错误</button>
        <button class="test-button" onclick="clearResults()">清除结果</button>
        
        <div id="error-result" class="result"></div>
    </div>

    <div class="test-section">
        <h3>🔍 数据结构检查</h3>
        <p>检查API返回的数据结构</p>
        
        <input type="text" id="check-version" value="TIC生产发布-250812" style="padding: 8px; width: 200px; margin-right: 10px;">
        <button class="test-button" onclick="checkDataStructure()">检查数据结构</button>
        
        <div id="structure-result" class="result"></div>
    </div>

    <div class="test-section">
        <h3>🧪 模拟数据测试</h3>
        <p>使用模拟数据测试数据处理逻辑</p>
        
        <button class="test-button" onclick="testWithMockData()">测试模拟数据</button>
        
        <div id="mock-result" class="result"></div>
    </div>

    <div class="test-section">
        <h3>🚀 打开实际页面</h3>
        <button class="test-button" onclick="openRealPage()">打开Release发布流程页面</button>
        <div class="info">
            <strong>调试建议：</strong>
            <ol>
                <li>打开页面后按F12打开控制台</li>
                <li>选择第一个版本查询</li>
                <li>切换到第二个版本查询</li>
                <li>观察控制台的详细错误信息</li>
            </ol>
        </div>
    </div>

    <script>
        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const color = type === 'error' ? '#f44336' : (type === 'success' ? '#4CAF50' : '#333');
            return `<div style="color: ${color};">[${timestamp}] ${message}</div>`;
        }

        function clearResults() {
            document.getElementById('error-result').innerHTML = '';
            document.getElementById('structure-result').innerHTML = '';
            document.getElementById('mock-result').innerHTML = '';
        }

        // 重现版本切换错误
        async function testVersionSwitchError() {
            const version1 = document.getElementById('version1').value;
            const version2 = document.getElementById('version2').value;
            const resultDiv = document.getElementById('error-result');
            
            resultDiv.innerHTML = log('开始重现版本切换错误...');
            
            try {
                // 第一次查询
                resultDiv.innerHTML += log(`步骤1: 查询版本 ${version1}`);
                const data1 = await callAPI(version1);
                resultDiv.innerHTML += log(`✓ 第一次查询成功`, 'success');
                resultDiv.innerHTML += log(`数据结构: ${JSON.stringify(Object.keys(data1.data || {}))}`);
                
                // 模拟处理数据
                const processResult1 = processIssueData(data1.data);
                resultDiv.innerHTML += log(`✓ 第一次数据处理成功: ${processResult1.summary}`, 'success');
                
                // 等待一秒
                await new Promise(resolve => setTimeout(resolve, 1000));
                
                // 第二次查询
                resultDiv.innerHTML += log(`步骤2: 切换到版本 ${version2}`);
                const data2 = await callAPI(version2);
                resultDiv.innerHTML += log(`✓ 第二次查询成功`, 'success');
                resultDiv.innerHTML += log(`数据结构: ${JSON.stringify(Object.keys(data2.data || {}))}`);
                
                // 模拟处理数据
                const processResult2 = processIssueData(data2.data);
                resultDiv.innerHTML += log(`✓ 第二次数据处理成功: ${processResult2.summary}`, 'success');
                
                resultDiv.innerHTML += log(`✅ 版本切换测试完成，未发现错误`, 'success');
                
            } catch (error) {
                resultDiv.innerHTML += log(`❌ 发生错误: ${error.message}`, 'error');
                resultDiv.innerHTML += log(`错误详情: ${error.stack}`, 'error');
            }
        }

        // 检查数据结构
        async function checkDataStructure() {
            const version = document.getElementById('check-version').value;
            const resultDiv = document.getElementById('structure-result');
            
            resultDiv.innerHTML = log(`检查版本 ${version} 的数据结构...`);
            
            try {
                const data = await callAPI(version);
                
                resultDiv.innerHTML += log(`✓ API调用成功`, 'success');
                resultDiv.innerHTML += log(`完整响应: ${JSON.stringify(data, null, 2)}`);
                
                if (data.data) {
                    const dataKeys = Object.keys(data.data);
                    resultDiv.innerHTML += log(`data字段的keys: [${dataKeys.join(', ')}]`);
                    
                    // 检查每个key的数据类型和长度
                    dataKeys.forEach(key => {
                        const value = data.data[key];
                        const type = Array.isArray(value) ? `Array(${value.length})` : typeof value;
                        resultDiv.innerHTML += log(`  ${key}: ${type}`);
                        
                        if (Array.isArray(value) && value.length > 0) {
                            resultDiv.innerHTML += log(`    第一个元素: ${JSON.stringify(value[0])}`);
                        }
                    });
                } else {
                    resultDiv.innerHTML += log(`❌ 缺少data字段`, 'error');
                }
                
            } catch (error) {
                resultDiv.innerHTML += log(`❌ 检查失败: ${error.message}`, 'error');
            }
        }

        // 测试模拟数据
        function testWithMockData() {
            const resultDiv = document.getElementById('mock-result');
            
            resultDiv.innerHTML = log('测试模拟数据处理...');
            
            // 模拟正确的数据结构
            const mockData = {
                Task: [
                    {
                        key: "TIC-32923",
                        summary: "调用ProCert Prod API检查阿里存量数据结果",
                        assignee: "Kilian_Shen",
                        status: "Done",
                        components: ["TIC Open"]
                    }
                ],
                Story: [],
                Bug: []
            };
            
            try {
                const result = processIssueData(mockData);
                resultDiv.innerHTML += log(`✓ 模拟数据处理成功: ${result.summary}`, 'success');
                resultDiv.innerHTML += log(`生成的HTML长度: ${result.html.length} 字符`);
                
            } catch (error) {
                resultDiv.innerHTML += log(`❌ 模拟数据处理失败: ${error.message}`, 'error');
            }
        }

        // 调用API
        async function callAPI(releaseName) {
            const response = await fetch('http://10.169.128.35:8611/business/api/v1/tools/jira/getReleaseIssue', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    releaseName: releaseName
                })
            });

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            const data = await response.json();
            
            // 模拟queryRelease函数的数据处理逻辑
            if (data && data.data && data.data.taskMap) {
                return {
                    data: data.data.taskMap,
                    branchMap: data.data.branchMap || {}
                };
            }
            
            return data;
        }

        // 处理Issue数据（模拟loadRequirementsIssues中的逻辑）
        function processIssueData(data) {
            if (!data) {
                throw new Error('数据为空');
            }
            
            let html = '';
            let totalCount = 0;
            
            ['Story', 'Task', 'Bug'].forEach(type => {
                if (data[type] && Array.isArray(data[type]) && data[type].length > 0) {
                    totalCount += data[type].length;
                    html += `<div class="issue-section"><h4>${type} (${data[type].length})</h4></div>`;
                }
            });
            
            if (html === '') {
                const dataKeys = Object.keys(data);
                html = `<div class="info">暂无Issue信息<br>数据结构: ${dataKeys.join(', ')}</div>`;
            }
            
            return {
                html: html,
                summary: `处理了 ${totalCount} 个Issue`
            };
        }

        // 打开实际页面
        function openRealPage() {
            window.open('./releaseProcess.html', '_blank');
        }
    </script>
</body>
</html>
