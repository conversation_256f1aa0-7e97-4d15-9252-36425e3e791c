# Release查询功能更新日志

## 新增功能：历史release版本查询 + 用户体验优化

### 功能描述
在"Release查询"二级菜单中新增了"历史release版本"功能，用户在选择Release版本号后点击查询，会在发布清单和发布检查清单之间显示历史release版本信息。

### 实现细节

#### 1. 新增API接口调用
- **接口地址**: `http://10.169.128.35:8611/business/api/v1/tools/jira/getChangeLog`
- **请求方法**: POST
- **请求参数**: 
  ```json
  {
    "releaseName": "TIC生产发布-250724"
  }
  ```
- **返回参数示例**:
  ```json
  {
    "data": [
      "TIC生产发布-250724（智能客服上线）",
      "智能客服上线",
      "172WK2待排",
      "TIC生产发布-250612",
      "TIC生产发布-250722",
      "TIC生产发布-250620",
      "TIC生产发布-250522",
      "TIC UAT发布-250403",
      "TIC生产发布-250724（智能客服+MIN KJ上线）",
      "TIC UAT发布-250716",
      "TIC UAT发布-250714",
      "智能客服第一期sp143wk2",
      "TIC UAT发布-250415",
      "TIC生产发布-250715",
      "TIC生产发布-250327",
      "TIC生产发布-250724",
      "TIC生产发布-250408",
      "TIC生产发布-250717"
    ],
    "resultCode": "0",
    "resultMsg": "成功"
  }
  ```

#### 2. 代码修改

##### 新增函数
- `getChangeLog(releaseName)`: 调用历史版本查询接口
- `generatePageFramework()`: 生成页面框架结构
- `fillReleaseData(response)`: 异步填充发布清单数据
- `fillIssuesContent(data)`: 填充Issue内容
- `fillGitReposContent(groupedApps, branchMap)`: 填充Git仓库内容
- `loadHistoryVersions(releaseName)`: 加载并显示历史版本信息

##### 重构函数
- `formatResults(response)`: 重构为先返回页面框架，再异步填充数据的模式
- 主查询事件处理函数: 优化为立即显示框架，然后异步加载各部分数据

#### 3. UI界面更新

##### HTML结构
在查询结果中新增了"历史release版本"区域，包含：
- 区域标题
- 加载状态提示
- 历史版本列表容器

##### CSS样式
新增了以下样式类：
- `.history-versions-container`: 历史版本容器样式
- `.history-versions-list`: 历史版本列表网格布局
- `.history-version-item`: 单个历史版本项样式
- `.version-index`: 版本序号圆形标签样式
- `.version-name`: 版本名称样式

#### 4. 用户体验优化

**核心改进：先显示页面框架，再异步填充数据**

1. **页面框架立即显示**:
   - 用户点击查询后立即显示完整的页面结构
   - 各个区域显示加载状态，用户可以看到页面布局
   - 避免了等待所有接口完成才显示页面的问题

2. **异步数据填充**:
   - 历史版本、Issue信息、Git仓库信息分别异步加载
   - 每个区域独立完成加载，互不影响
   - 加载完成后平滑切换到实际内容

3. **平滑过渡效果**:
   - 加载状态到内容显示的平滑过渡动画
   - 透明度渐变效果，提升视觉体验
   - 旋转加载图标，明确表示正在加载

4. **自适应布局**:
   - 响应式设计，支持桌面和移动设备
   - 内容区域自适应高度，避免布局跳动
   - 网格布局自动适应屏幕尺寸

5. **错误处理优化**:
   - 各区域独立的错误处理
   - 友好的错误提示信息
   - 错误状态也有平滑过渡效果

#### 5. 测试方法

1. 启动本地服务器：
   ```bash
   cd release
   python -m http.server 8080
   ```

2. 在浏览器中访问：`http://localhost:8080/release.html`

3. 选择一个Release版本号并点击查询

4. 查看结果页面中是否显示"历史release版本"区域

### 技术栈
- 前端：原生JavaScript + HTML + CSS
- 接口：RESTful API
- 布局：CSS Grid + Flexbox

### 兼容性
- 支持现代浏览器（Chrome、Firefox、Safari、Edge）
- 响应式设计，支持桌面和移动设备
