# 查询按钮刷新问题修复

## 问题描述
用户反馈：选择release信息后，切换其他release版本再点击查询，不会重新调用接口刷新页面数据。

## 根本原因分析
1. **缓存机制问题**：之前的逻辑只在版本变化时才清除缓存，导致相同版本重复查询时使用缓存数据
2. **强制刷新逻辑不完整**：虽然传递了`forceRefresh=true`参数，但缓存清除不彻底
3. **日志不足**：缺少详细的调试日志，难以跟踪问题

## 修复方案

### 1. 修改表单提交事件
**位置**：`release/releaseProcess.js` 第612-681行

**修复内容**：
- 每次点击查询按钮都强制清除所有缓存
- 添加详细的控制台日志跟踪
- 使用`await`确保异步操作完成

**修复代码**：
```javascript
// 每次点击查询都强制刷新，不管版本是否变化
console.log(`🔍 点击查询按钮: ${version}${isVersionChanged ? ` (从 ${oldVersion} 切换)` : ' (重新查询)'}`);

// 强制清除所有缓存
console.log(`🗑️ 强制清除所有缓存`);
Object.keys(releaseCache).forEach(key => {
    delete releaseCache[key];
});
cachedBranchMapKeys = [];
```

### 2. 增强缓存清除函数
**位置**：`release/releaseProcess.js` 第350-362行

**修复内容**：
- 添加详细的日志输出
- 明确显示缓存清除状态

**修复代码**：
```javascript
function clearReleaseCache(releaseName) {
    console.log(`🗑️ 清除缓存: ${releaseName}`);
    if (releaseName && releaseCache[releaseName]) {
        delete releaseCache[releaseName];
        console.log(`✓ 已删除 ${releaseName} 的缓存`);
    } else {
        console.log(`ℹ️ ${releaseName} 没有缓存数据`);
    }
    // 同时清除branchMap keys缓存
    cachedBranchMapKeys = [];
    console.log(`✓ 已清除branchMap keys缓存`);
}
```

### 3. 增强API调用日志
**位置**：`release/releaseProcess.js` 第76-90行

**修复内容**：
- 添加API调用状态日志
- 显示缓存使用情况

**关键日志**：
```javascript
console.log(`queryRelease调用: releaseName=${releaseName}, forceRefresh=${forceRefresh}, hasCache=${hasCache}`);
console.log(`🔄 正在调用API查询Release信息: ${releaseName}`);
```

## 修复效果

### 预期行为
每次点击查询按钮时，无论版本是否变化，都会：
1. 强制清除所有缓存
2. 重新调用API获取最新数据
3. 刷新当前激活的Tab内容
4. 更新branchMap keys缓存

### 控制台日志输出
正常情况下，每次点击查询按钮应该看到以下日志序列：
```
🔍 点击查询按钮: TIC生产发布-250812 (重新查询)
🗑️ 强制清除所有缓存
📋 当前激活tab: requirements，强制刷新数据
📋 loadRequirementsIssues调用: releaseName=TIC生产发布-250812, forceRefresh=true
queryRelease调用: releaseName=TIC生产发布-250812, forceRefresh=true, hasCache=false
🔄 正在调用API查询Release信息: TIC生产发布-250812
缓存branchMap keys: [TIC Mall View, TIC Mall]
✅ 查询完成: TIC生产发布-250812
```

## 测试方法

### 1. 使用测试页面
打开 `test_query_button.html` 页面，按照测试步骤进行验证。

### 2. 手动测试步骤
1. 打开 `releaseProcess.html` 页面
2. 按F12打开开发者工具控制台
3. 选择一个版本号，点击查询
4. 观察控制台日志
5. 不切换版本，再次点击查询
6. 观察是否再次调用API
7. 切换版本后再次测试

### 3. 验证要点
- [x] 每次点击查询都有完整的日志输出
- [x] 每次都调用API接口（看到🔄日志）
- [x] 缓存被正确清除（看到🗑️日志）
- [x] branchMap keys被正确缓存
- [x] Tab内容被正确刷新

## 相关文件

### 修改的文件
- `release/releaseProcess.js` - 主要修复文件

### 新增的测试文件
- `release/test_query_button.html` - 查询按钮测试页面
- `release/QUERY_BUTTON_FIX.md` - 本修复说明文档

## 调试工具

在浏览器控制台中可以使用以下命令进行调试：
```javascript
// 查看当前缓存的branchMap keys
debugBranchMapKeys()

// 获取缓存数据
getCachedBranchMapKeys()

// 查看当前选择的版本
console.log('当前版本:', currentReleaseName)
```

## 总结

通过这次修复，确保了：
1. **每次点击查询按钮都会重新调用API**
2. **缓存机制不会阻止数据刷新**
3. **详细的日志便于问题跟踪**
4. **branchMap keys正确缓存和更新**

现在用户可以放心地切换不同的release版本，每次查询都会获取到最新的数据。
