<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <title>Release发布流程测试</title>
    <style>
        body {
            margin: 20px;
            font-family: Arial, sans-serif;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        .test-button {
            padding: 10px 20px;
            margin: 5px;
            background: #4CAF50;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        .test-button:hover {
            background: #45a049;
        }
        .result {
            margin-top: 10px;
            padding: 10px;
            background: #f5f5f5;
            border-radius: 4px;
            white-space: pre-wrap;
        }
    </style>
</head>
<body>
    <h1>Release发布流程功能测试</h1>
    
    <div class="test-section">
        <h3>1. 测试版本号加载</h3>
        <button class="test-button" onclick="testLoadVersions()">测试加载版本号</button>
        <div id="versions-result" class="result"></div>
    </div>

    <div class="test-section">
        <h3>2. 测试Release信息查询</h3>
        <input type="text" id="test-version" placeholder="输入版本号" style="padding: 8px; margin-right: 10px;">
        <button class="test-button" onclick="testQueryRelease()">测试查询Release</button>
        <div id="release-result" class="result"></div>
    </div>

    <div class="test-section">
        <h3>3. 测试分支检查</h3>
        <input type="text" id="test-branch-version" placeholder="输入版本号" style="padding: 8px; margin-right: 10px;">
        <button class="test-button" onclick="testCheckBranch()">测试分支检查</button>
        <div id="branch-result" class="result"></div>
    </div>

    <div class="test-section">
        <h3>4. 测试历史版本查询</h3>
        <input type="text" id="test-history-version" placeholder="输入版本号" style="padding: 8px; margin-right: 10px;">
        <button class="test-button" onclick="testGetChangeLog()">测试历史版本</button>
        <div id="history-result" class="result"></div>
    </div>

    <div class="test-section">
        <h3>5. 打开完整页面</h3>
        <button class="test-button" onclick="openFullPage()">打开Release发布流程页面</button>
    </div>

    <script>
        // 测试加载版本号
        async function testLoadVersions() {
            const resultDiv = document.getElementById('versions-result');
            resultDiv.textContent = '加载中...';
            
            try {
                const response = await fetch('http://*************:8611/business/api/v1/tools/jira/getReleases', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        limit: 10,
                        projectGroup: "TIC"
                    })
                });

                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }

                const data = await response.json();
                const versions = Array.isArray(data) ? data : (data.data || data.versions || []);
                
                resultDiv.textContent = `成功加载 ${versions.length} 个版本:\n` + 
                    versions.slice(0, 5).map(v => v.name || v.versionName || v.version || v).join('\n');
            } catch (error) {
                resultDiv.textContent = `错误: ${error.message}`;
            }
        }

        // 测试查询Release信息
        async function testQueryRelease() {
            const version = document.getElementById('test-version').value;
            const resultDiv = document.getElementById('release-result');
            
            if (!version) {
                resultDiv.textContent = '请输入版本号';
                return;
            }
            
            resultDiv.textContent = '查询中...';
            
            try {
                const response = await fetch('http://*************:8611/business/api/v1/tools/jira/getReleaseIssue', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        releaseName: version
                    })
                });

                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }

                const data = await response.json();
                resultDiv.textContent = `查询成功:\n${JSON.stringify(data, null, 2)}`;
            } catch (error) {
                resultDiv.textContent = `错误: ${error.message}`;
            }
        }

        // 测试分支检查
        async function testCheckBranch() {
            const version = document.getElementById('test-branch-version').value;
            const resultDiv = document.getElementById('branch-result');
            
            if (!version) {
                resultDiv.textContent = '请输入版本号';
                return;
            }
            
            resultDiv.textContent = '查询中...';
            
            try {
                const response = await fetch('http://*************:8611/business/api/v1/release/release/checkBranch', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        releaseName: version
                    })
                });

                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }

                const data = await response.json();
                resultDiv.textContent = `分支检查成功:\n${JSON.stringify(data, null, 2)}`;
            } catch (error) {
                resultDiv.textContent = `错误: ${error.message}`;
            }
        }

        // 测试历史版本查询
        async function testGetChangeLog() {
            const version = document.getElementById('test-history-version').value;
            const resultDiv = document.getElementById('history-result');
            
            if (!version) {
                resultDiv.textContent = '请输入版本号';
                return;
            }
            
            resultDiv.textContent = '查询中...';
            
            try {
                const response = await fetch('http://*************:8611/business/api/v1/tools/jira/getChangeLog', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        releaseName: version
                    })
                });

                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }

                const data = await response.json();
                resultDiv.textContent = `历史版本查询成功:\n${JSON.stringify(data, null, 2)}`;
            } catch (error) {
                resultDiv.textContent = `错误: ${error.message}`;
            }
        }

        // 打开完整页面
        function openFullPage() {
            window.open('./releaseProcess.html', '_blank');
        }
    </script>
</body>
</html>
