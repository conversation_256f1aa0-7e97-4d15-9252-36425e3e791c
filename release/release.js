// 存储所有版本号
let allVersions = [];

// 添加缓存对象，用于存储已查询的Release信息
const releaseCache = {};

// 加载版本号列表
async function loadReleaseVersions() {
    try {
        const response = await fetch('http://10.169.128.35:8611/business/api/v1/tools/jira/getReleases', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                limit: 50,
                projectGroup: "TIC"
            })
        });

        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        const data = await response.json();
        
        allVersions = Array.isArray(data) ? data : (data.data || data.versions || []);
        
        if (!Array.isArray(allVersions)) {
            throw new Error('无效的数据格式');
        }

        updateVersionOptions(allVersions);
    } catch (error) {
        console.error('加载版本号列表失败：', error);
        document.getElementById('result').innerHTML = `<div class="error">加载版本号列表失败: ${error.message}</div>`;
    }
}

// 更新版本号选项
function updateVersionOptions(versions) {
    const optionsContainer = document.getElementById('versionOptions');
    optionsContainer.innerHTML = '';
    
    versions.forEach(version => {
        const versionName = version.name || version.versionName || version.version || version;
        const option = document.createElement('div');
        option.className = 'select-option';
        option.textContent = versionName;
        option.onclick = () => selectVersion(versionName);
        optionsContainer.appendChild(option);
    });
}

// 选择版本号
function selectVersion(version) {
    const searchInput = document.getElementById('versionSearch');
    searchInput.value = version;
    document.getElementById('versionOptions').style.display = 'none';
}

// 搜索过滤版本号
function filterVersions(searchText) {
    const filtered = allVersions.filter(version => {
        const versionName = version.name || version.versionName || version.version || version;
        return versionName.toLowerCase().includes(searchText.toLowerCase());
    });
    updateVersionOptions(filtered);
}

// 查询Release信息（添加缓存机制）
async function queryRelease(releaseName) {
    // 检查缓存中是否已有数据
    if (releaseCache[releaseName]) {
        console.log(`使用缓存的Release信息: ${releaseName}`);
        return releaseCache[releaseName];
    }

    try {
        console.log(`查询Release信息: ${releaseName}`);
        const response = await fetch('http://10.169.128.35:8611/business/api/v1/tools/jira/getReleaseIssue', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
//                releaseName: "'" + releaseName + "'"
                releaseName: releaseName
            })
        });

        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        const data = await response.json();
        let result;

        // 处理新的数据结构，返回包含taskMap和branchMap的完整数据
        if (data && data.data) {
            if (data.data.taskMap) {
                // 保留原始branchMap数据
                result = {
                    data: data.data.taskMap,
                    branchMap: data.data.branchMap || {}
                };
            } else {
                result = data;
            }
        } else {
            result = data;
        }

        // 将结果存入缓存
        releaseCache[releaseName] = result;
        return result;
    } catch (error) {
        throw new Error(`查询失败: ${error.message}`);
    }
}

// 查询历史Release版本信息
async function getChangeLog(releaseName) {
    try {
        console.log(`查询历史Release版本: ${releaseName}`);
        const response = await fetch('http://10.169.128.35:8611/business/api/v1/tools/jira/getChangeLog', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                releaseName: releaseName
            })
        });

        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        const data = await response.json();

        // 检查返回结果
        if (data.resultCode === "0" && data.data && Array.isArray(data.data)) {
            return data.data;
        } else {
            console.warn('历史版本查询返回异常:', data);
            return [];
        }
    } catch (error) {
        console.error('查询历史Release版本失败:', error);
        return [];
    }
}

// 加载并显示历史版本信息
async function loadHistoryVersions(releaseName) {
    try {
        // 获取历史版本数据
        const historyVersions = await getChangeLog(releaseName);

        // 获取显示容器
        const loadingElement = document.getElementById('history-versions-loading');
        const contentElement = document.getElementById('history-versions-content');

        if (!loadingElement || !contentElement) {
            console.warn('历史版本显示容器未找到');
            return;
        }

        // 生成历史版本HTML
        let html = '';

        if (historyVersions.length === 0) {
            html = '<div class="info">暂无历史版本信息</div>';
        } else {
            html = `
                <div class="history-versions-list">
                    ${historyVersions.map((version, index) => `
                        <div class="history-version-item">
                            <span class="version-index">${index + 1}</span>
                            <span class="version-name">${version}</span>
                        </div>
                    `).join('')}
                </div>
            `;
        }

        // 平滑切换：先隐藏加载中，然后显示内容
        loadingElement.style.opacity = '0';
        setTimeout(() => {
            loadingElement.style.display = 'none';
            contentElement.innerHTML = html;
            contentElement.style.display = 'block';
            // 触发重排后添加透明度过渡
            setTimeout(() => {
                contentElement.style.opacity = '1';
            }, 10);
        }, 300);

        console.log(`已加载 ${historyVersions.length} 个历史版本`);
    } catch (error) {
        console.error('加载历史版本失败:', error);

        // 显示错误信息
        const loadingElement = document.getElementById('history-versions-loading');
        const contentElement = document.getElementById('history-versions-content');

        if (loadingElement && contentElement) {
            loadingElement.style.opacity = '0';
            setTimeout(() => {
                loadingElement.style.display = 'none';
                contentElement.innerHTML = '<div class="error">加载历史版本失败</div>';
                contentElement.style.display = 'block';
                setTimeout(() => {
                    contentElement.style.opacity = '1';
                }, 10);
            }, 300);
        }
    }
}

// 生成页面框架结构
function generatePageFramework() {
    return `
        <!-- 历史release版本区域 -->
        <div class="release-section">
            <h2 class="section-title">历史release版本</h2>
            <div class="history-versions-container">
                <div class="loading" id="history-versions-loading">加载历史版本中...</div>
                <div class="history-versions-content" id="history-versions-content" style="display: none;">
                    <!-- 历史版本内容将通过JS动态填充 -->
                </div>
            </div>
        </div>

        <!-- 发布清单区域 -->
        <div class="release-section">
            <h2 class="section-title">发布清单</h2>
            <div class="release-info">
                <!-- 左侧Issue列表 -->
                <div class="issues-container">
                    <div class="loading" id="issues-loading">加载Issue信息中...</div>
                    <div id="issues-content" style="display: none;">
                        <!-- Issue内容将通过JS动态填充 -->
                    </div>
                </div>
                <!-- 右侧Git仓库信息 -->
                <div class="git-repos-section">
                    <h3>涉及Git仓库工程</h3>
                    <div class="loading" id="git-repos-loading">加载Git仓库信息中...</div>
                    <div id="git-repos-content" style="display: none;">
                        <!-- Git仓库内容将通过JS动态填充 -->
                    </div>
                </div>
            </div>
        </div>

        <!-- 发布检查清单区域 -->
        <div class="release-section">
            <h2 class="section-title">发布检查清单</h2>
            <div class="tabs-container">
                <div class="tabs">
                    <div class="tab active" data-tab="daily">日常Release发布</div>
                    <div class="tab" data-tab="major">大版本割接计划</div>
                </div>

                <div class="tab-content active" id="daily-tab">
                    <div class="checklist-options">
                        <div class="checklist-option-group">
                            <h4>发布环境</h4>
                            <div class="option-items horizontal">
                                <div class="checklist-item">
                                    <input type="radio" id="dailyEnvUat" name="dailyEnvironment" value="UAT" onchange="generateReleaseList(document.getElementById('versionSearch').value, 'uat')">
                                    <label for="dailyEnvUat">UAT环境</label>
                                </div>
                                <div class="checklist-item">
                                    <input type="radio" id="dailyEnvProd" name="dailyEnvironment" value="prod" onchange="generateReleaseList(document.getElementById('versionSearch').value, 'prod')">
                                    <label for="dailyEnvProd">PROD环境</label>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="tab-content" id="major-tab">
                    <div class="checklist-options">
                        <div class="checklist-option-group">
                            <h4>发布环境</h4>
                            <div class="option-items horizontal">
                                <div class="checklist-item">
                                    <input type="radio" id="majorEnvUat" name="majorEnvironment" value="UAT" onchange="generateMajorReleaseList(document.getElementById('versionSearch').value, 'uat')">
                                    <label for="majorEnvUat">UAT环境</label>
                                </div>
                                <div class="checklist-item">
                                    <input type="radio" id="majorEnvProd" name="majorEnvironment" value="prod" onchange="generateMajorReleaseList(document.getElementById('versionSearch').value, 'prod')">
                                    <label for="majorEnvProd">PROD环境</label>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    `;
}

function formatResults(response) {
    if (!response || !response.data) {
        return '<div class="error">未找到相关Release信息</div>';
    }

    // 先返回页面框架，然后异步填充数据
    const framework = generatePageFramework();

    // 异步填充Issue和Git仓库数据
    fillReleaseData(response);

    return framework;
}

// 异步填充发布清单数据
async function fillReleaseData(response) {
    try {
        const { data, branchMap = {} } = response;

        // 加载app.json
        const appDataResponse = await fetch('../json/app.json');
        const appData = await appDataResponse.json();

        // 按appHierarchy对应用进行分组
        const groupedApps = {};

        // 收集所有issues的components
        const allIssues = [...(data.Story || []), ...(data.Task || []), ...(data.Bug || [])];
        const componentGitMap = new Map();

        // 遍历所有issues的components，与app.json匹配
        allIssues.forEach(issue => {
            (issue.components || []).forEach(comp => {
                const matchedApp = appData.find(app => app.component === comp);
                if (matchedApp) {
                    if (!groupedApps[matchedApp.appHierarchy]) {
                        groupedApps[matchedApp.appHierarchy] = new Set();
                    }
                    if (matchedApp.gitName) {
                        groupedApps[matchedApp.appHierarchy].add(matchedApp.gitName);
                    }
                    componentGitMap.set(comp, matchedApp.gitName);
                }
            });
        });

        // 填充Issue内容
        fillIssuesContent(data);

        // 填充Git仓库内容
        fillGitReposContent(groupedApps, branchMap);

    } catch (error) {
        console.error('填充发布清单数据失败：', error);

        // 显示错误信息
        const issuesLoading = document.getElementById('issues-loading');
        const gitReposLoading = document.getElementById('git-repos-loading');

        if (issuesLoading) {
            issuesLoading.innerHTML = '<div class="error">加载Issue信息失败</div>';
        }

        if (gitReposLoading) {
            gitReposLoading.innerHTML = '<div class="error">加载Git仓库信息失败</div>';
        }
    }
}

// 填充Issue内容
function fillIssuesContent(data) {
    const issuesLoading = document.getElementById('issues-loading');
    const issuesContent = document.getElementById('issues-content');

    if (!issuesLoading || !issuesContent) {
        console.warn('Issues容器未找到');
        return;
    }

    let html = '';

    // 生成Issue列表
    ['Story', 'Task', 'Bug'].forEach(type => {
        if (data[type] && data[type].length > 0) {
            html += `
                <div class="section">
                    <div class="section-header">
                        <h4>${type} (${data[type].length})</h4>
                        <button class="toggle-btn" onclick="toggleSection(this)">+</button>
                    </div>
                    <div class="section-content" style="display: none;">
                        <table>
                            <thead>
                                <tr>
                                    <th>Issue</th>
                                    <th>Title</th>
                                    <th>Assignee</th>
                                    <th>Components</th>
                                    <th>Status</th>
                                </tr>
                            </thead>
                            <tbody>
                                ${data[type].map(item => `
                                    <tr>
                                        <td>${item.key || '-'}</td>
                                        <td><a href="https://jira.sgsonline.com.cn/browse/${item.key}" target="_blank">${item.summary || '-'}</a></td>
                                        <td>${item.assignee || '-'}</td>
                                        <td>${Array.isArray(item.components) ? item.components.join(', ') : '-'}</td>
                                        <td>${item.status || '-'}</td>
                                    </tr>
                                `).join('')}
                            </tbody>
                        </table>
                    </div>
                </div>
            `;
        }
    });

    // 平滑切换：先隐藏加载中，然后显示内容
    issuesLoading.style.opacity = '0';
    setTimeout(() => {
        issuesLoading.style.display = 'none';
        issuesContent.innerHTML = html;
        issuesContent.style.display = 'block';
        // 触发重排后添加透明度过渡
        setTimeout(() => {
            issuesContent.style.opacity = '1';
        }, 10);
    }, 300);
}

// 填充Git仓库内容
function fillGitReposContent(groupedApps, branchMap) {
    const gitReposLoading = document.getElementById('git-repos-loading');
    const gitReposContent = document.getElementById('git-repos-content');

    if (!gitReposLoading || !gitReposContent) {
        console.warn('Git仓库容器未找到');
        return;
    }

    let html = '';

    // 生成Git仓库信息
    html += Object.entries(groupedApps).map(([hierarchy, gitNames]) => `
        <div class="hierarchy-group">
            <h4>${hierarchy}</h4>
            <ul>
                ${Array.from(gitNames).map(gitName => `
                    <li>${gitName}</li>
                `).join('')}
            </ul>
        </div>
    `).join('');

    // 添加分支信息
    if (Object.keys(branchMap).length > 0) {
        html += `
            <div class="branch-info">
                <h4>分支信息</h4>
                ${Object.entries(branchMap).map(([component, branches]) => `
                    <div class="component-branch">
                        <h5>${component}</h5>
                        ${branches.length > 0 ? `
                            <ul>
                                ${branches.map(branch => `<li>${branch}</li>`).join('')}
                            </ul>
                        ` : '<p>无分支信息</p>'}
                    </div>
                `).join('')}
            </div>
        `;
    }

    // 如果没有数据，显示提示信息
    if (html === '') {
        html = '<div class="info">暂无Git仓库信息</div>';
    }

    // 平滑切换：先隐藏加载中，然后显示内容
    gitReposLoading.style.opacity = '0';
    setTimeout(() => {
        gitReposLoading.style.display = 'none';
        gitReposContent.innerHTML = html;
        gitReposContent.style.display = 'block';
        // 触发重排后添加透明度过渡
        setTimeout(() => {
            gitReposContent.style.opacity = '1';
        }, 10);
    }, 300);
}

// 切换section的显示/隐藏
function toggleSection(button) {
    const section = button.closest('.section');
    const content = section.querySelector('.section-content');
    
    if (content.style.display === 'none' || content.style.display === '') {
        // 展开
        content.style.display = 'block';
        content.style.maxHeight = '0';
        // 触发重排以使动画生效
        content.offsetHeight;
        content.style.maxHeight = content.scrollHeight + 'px';
        button.textContent = '-';
    } else {
        // 收起
        content.style.maxHeight = '0';
        button.textContent = '+';
        // 等待动画完成后隐藏
        setTimeout(() => {
            if (content.style.maxHeight === '0px') {
                content.style.display = 'none';
            }
        }, 300);
    }
}

// 从服务端获取发布清单数据
async function getReleaseData(releaseName, env) {
    try {
        const response = await fetch('http://10.169.128.35:8611/business/api/v1/release/release/getRelease', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                releaseName: releaseName,
                env: env.toUpperCase()
            })
        });

        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        const result = await response.json();
        
        if (result.resultCode !== "0") {
            throw new Error(`API error: ${result.resultMsg}`);
        }
        
        // 获取issue信息（使用缓存机制）
        const issueData = await queryRelease(releaseName);
        const allIssues = [];
        
        if (issueData && issueData.data) {
            // 收集所有issues的components
            ['Story', 'Task', 'Bug'].forEach(type => {
                if (issueData.data[type] && issueData.data[type].length > 0) {
                    allIssues.push(...issueData.data[type]);
                }
            });
        }
        
        // 将issue信息合并到releaseData中
        const releaseData = result.data || [];
        releaseData.forEach(item => {
            // 查找匹配的issue
            const matchedIssue = allIssues.find(issue => 
                issue.key && item.issue && issue.key.includes(item.issue)
            );
            
            if (matchedIssue) {
                item.components = matchedIssue.components || [];
            }
        });
        
        return releaseData;
    } catch (error) {
        console.error('获取发布清单数据失败：', error);
        throw error;
    }
}

// 生成发布清单
async function generateReleaseList(versionName, environment) {
    try {
        // 获取当前激活的tab
        const activeTab = document.querySelector('.tab.active');
        const tabType = activeTab ? activeTab.getAttribute('data-tab') : 'daily';
        
        // 如果是大版本割接计划且环境是UAT，则不显示发布清单
        if (tabType === 'major') {
            // 清除之前的内容
            const existingContainer = document.getElementById('releaseListContainer');
            if (existingContainer) {
                existingContainer.remove();
            }
            return;
        }
        
        // 清除之前的内容
        const releaseListContainer = document.getElementById('releaseListContainer') || document.createElement('div');
        releaseListContainer.id = 'releaseListContainer';
        releaseListContainer.className = 'release-section';
        
        // 显示加载中
        releaseListContainer.innerHTML = '<div class="loading">加载发布清单中...</div>';
        
        // 添加到页面
        const resultDiv = document.getElementById('result');
        const existingContainer = document.getElementById('releaseListContainer');
        if (existingContainer) {
            resultDiv.replaceChild(releaseListContainer, existingContainer);
        } else {
            resultDiv.appendChild(releaseListContainer);
        }
        
        // 从服务端获取数据
        const releaseData = await getReleaseData(versionName, environment);
        
        // 创建发布清单内容
        let html = `
            <h2 class="section-title">${environment.toUpperCase()} 环境发布清单</h2>
            <div class="release-list-content">
                <div class="tabs-container">
                    <div class="tabs">
                        <div class="tab active" data-tab="check-nodes">检查节点</div>
                        <div class="tab" data-tab="release-list">Release清单</div>
                        <div class="tab" data-tab="version-check">版本核对结果</div>
                    </div>
                    
                    <div class="tab-content active" id="check-nodes-tab">
                        <div class="check-nodes-container">
                            <!-- 添加开始检查按钮到顶部 -->
                            <div class="check-nodes-actions">
                                <button class="action-btn primary-btn" id="start-check-btn">开始检查</button>
                            </div>
                            
                            <!-- 分支检查节点 -->
                            <div class="check-node">
                                <div class="check-node-header">
                                    <div class="node-title">分支检查</div>
                                    <div class="node-info">检查各组件是否存在对应分支</div>
                                    <div class="node-result">
                                        <span class="status-badge status-pending">待检查</span>
                                    </div>
                                </div>
                                <div class="check-node-content">
                                    <div class="node-detail-container">
                                        <table>
                                            <thead>
                                                <tr>
                                                    <th>组件</th>
                                                    <th>分支名称</th>
                                                    <th>检查结果</th>
                                                </tr>
                                            </thead>
                                            <tbody id="branch-check-result">
                                                <!-- 分支检查结果将通过JS动态填充 -->
                                                <tr>
                                                    <td colspan="3">待检查，请点击开始检查按钮</td>
                                                </tr>
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- 分支合并检查节点 -->
                            <div class="check-node">
                                <div class="check-node-header">
                                    <div class="node-title">分支合并检查</div>
                                    <div class="node-info">检查各分支是否已合并到主分支</div>
                                    <div class="node-result">
                                        <span class="status-badge status-pending">待检查</span>
                                    </div>
                                </div>
                                <div class="check-node-content">
                                    <div class="node-detail-container">
                                        <table>
                                            <thead>
                                                <tr>
                                                    <th>组件</th>
                                                    <th>分支名称</th>
                                                    <th>目标分支</th>
                                                    <th>合并状态</th>
                                                </tr>
                                            </thead>
                                            <tbody id="merge-check-result">
                                                <!-- 分支合并检查结果将通过JS动态填充 -->
                                                <tr>
                                                    <td colspan="4">待检查，请点击开始检查按钮</td>
                                                </tr>
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="tab-content" id="release-list-tab">
        `;
        
        // 按发布类型分组
        const groupedByType = {};
        releaseData.forEach(item => {
            const type = item.releaseType || 'other';
            if (!groupedByType[type]) {
                groupedByType[type] = [];
            }
            groupedByType[type].push(item);
        });
        
        // 生成不同类型的发布区域
        const typeLabels = {
            'app': '应用发布',
            'db': '数据库发布',
            'nacos': 'Nacos配置发布'
        };
        
        // 按顺序显示不同类型
        ['app', 'db', 'nacos'].forEach(type => {
            if (groupedByType[type] && groupedByType[type].length > 0) {
                html += generateDeploymentSectionFromData(typeLabels[type], groupedByType[type]);
            }
        });

        html += `
                    </div>
                    
                    <div class="tab-content" id="version-check-tab">
                        <div class="deployment-section">
                            <div class="section-header">
                                <h4>版本核对结果</h4>
                            </div>
                            <div class="section-content">
                                <table>
                                    <thead>
                                        <tr>
                                            <th>Issue</th>
                                            <th>Component</th>
                                            <th>GitLab名称</th>
                                            <th>Release GitLab名称</th>
                                            <th>比对结果</th>
                                        </tr>
                                    </thead>
                                    <tbody id="version-check-content">
                                        <tr><td colspan="5">加载中...</td></tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;
        
        releaseListContainer.innerHTML = html;

        // 初始化发布清单中的Tab功能
        initReleaseListTabs(releaseListContainer);

        // 加载版本核对结果
        const versionCheckContent = await fetchIssueComponentsFromResult(versionName);
        const versionCheckContentElement = document.getElementById('version-check-content');
        if (versionCheckContentElement) {
            versionCheckContentElement.innerHTML = versionCheckContent;
        }

        // 为"开始检查"按钮添加事件监听器
        const startCheckBtn = document.getElementById('start-check-btn');
        if (startCheckBtn) {
            startCheckBtn.addEventListener('click', function() {
                startBranchAndMergeCheck(versionName);
            });
        }

        // 由于"检查节点"tab是默认选中的，自动开始检查
        startBranchAndMergeCheck(versionName);

        console.log(`已生成${environment}环境的发布清单`);
    } catch (error) {
        const releaseListContainer = document.getElementById('releaseListContainer');
        if (releaseListContainer) {
            releaseListContainer.innerHTML = `<div class="error">获取发布清单失败: ${error.message}</div>`;
        }
    }
}

// 根据实际数据生成部署区域HTML
function generateDeploymentSectionFromData(title, deployments) {
    // 获取发布类型
    const releaseType = deployments.length > 0 ? deployments[0].releaseType : 'app';
    
    // 根据发布类型设置表头文案
    let nameColumnTitle = '应用名称';
    if (releaseType === 'db') {
        nameColumnTitle = '数据库';
    } else if (releaseType === 'nacos') {
        nameColumnTitle = '配置文件';
    }
    
    let html = `
        <div class="deployment-section">
            <div class="section-header">
                <h4>${title}</h4>
            </div>
            <div class="section-content">
    `;
    
    // 按申请人分组
    const groupedByUser = {};
    deployments.forEach(item => {
        const user = item.applyUser || '未知';
        if (!groupedByUser[user]) {
            groupedByUser[user] = [];
        }
        groupedByUser[user].push(item);
    });
    
    // 生成每个申请人的部署项
    Object.keys(groupedByUser).sort().forEach(user => {
        html += `
            <div class="owner-section">
                <h5 class="owner-name">申请人: ${user}</h5>
                <table>
                    <thead>
                        <tr>
                            <th>${nameColumnTitle}</th>
                            <th>项目</th>
                            <th>环境</th>
                            <th>工单号</th>
                            <th>Issue</th>
                            <th>备注</th>
                            <th>状态</th>
                        </tr>
                    </thead>
                    <tbody>
        `;
        
        // 按应用名称或数据库名称分组
        const groupedByApp = {};
        groupedByUser[user].forEach(item => {
            // 对于DB类型，使用dbName作为分组键
            // 对于其他类型，使用appName作为分组键
            const releaseType = item.releaseType || 'app';
            const groupKey = (releaseType === 'db' && item.dbName) ? item.dbName : (item.appName || '-');
            
            if (!groupedByApp[groupKey]) {
                groupedByApp[groupKey] = {
                    project: item.project || '-',
                    envName: item.envName || '-',
                    releaseType: releaseType,
                    status: item.status || '待发布', // 保存状态字段
                    orders: [],
                    items: []
                };
            }
            
            // 如果当前项有状态且不是"待发布"，则更新组的状态
            // 这确保了如果组内有任何一项已经有明确状态，就使用该状态
            if (item.status && item.status !== '待发布') {
                groupedByApp[groupKey].status = item.status;
            }
            
            // 保存工单信息
            if (item.orderNo) {
                groupedByApp[groupKey].orders.push({
                    orderNo: item.orderNo,
                    issue: item.issue || '-',
                    remark: item.remark || '-'
                });
            }
            
            // 保存原始项
            groupedByApp[groupKey].items.push(item);
        });
        
        // 生成每个应用的行
        Object.keys(groupedByApp).sort().forEach(appName => {
            const appGroup = groupedByApp[appName];
            const orders = appGroup.orders;
            
            // 获取该应用组的第一个项目，用于确定发布类型和dbName
            const firstItem = appGroup.items[0] || {};
            const releaseType = firstItem.releaseType || 'app';
            const displayName = releaseType === 'db' && firstItem.dbName ? firstItem.dbName : appName;
            
            if (orders.length === 0) {
                // 如果没有工单，显示一行
                html += `
                    <tr>
                        <td>${displayName}</td>
                        <td>${appGroup.project}</td>
                        <td>${appGroup.envName}</td>
                        <td>-</td>
                        <td>-</td>
                        <td>-</td>
                        <td>
                            <span class="status-badge ${getStatusClass(appGroup.status)}">
                                ${appGroup.status || '待发布'}
                            </span>
                        </td>
                    </tr>
                `;
            } else {
                // 有工单，每个工单显示一行
                orders.forEach((order, index) => {
                    if (index === 0) {
                        // 第一行显示应用信息
                        html += `
                            <tr>
                                <td rowspan="${orders.length}">${displayName}</td>
                                <td rowspan="${orders.length}">${appGroup.project}</td>
                                <td rowspan="${orders.length}">${appGroup.envName}</td>
                                <td>${order.orderNo}</td>
                                <td>${order.issue}</td>
                                <td>${order.remark}</td>
                                <td rowspan="${orders.length}">
                            		<span class="status-badge ${getStatusClass(appGroup.status)}">
                                		${appGroup.status || '待发布'}
                                    </span>
                                </td>
                            </tr>
                        `;
                    } else {
                        // 后续行只显示工单信息
                        html += `
                            <tr>
                                <td>${order.orderNo}</td>
                                <td>${order.issue}</td>
                                <td>${order.remark}</td>
                            </tr>
                        `;
                    }
                });
            }
        });
        
        html += `
                    </tbody>
                </table>
            </div>
        `;
    });
    
    html += `
            </div>
        </div>
    `;
    
    return html;
}

// 生成并下载Markdown文件
function generateMarkdownFile(versionName, checklistType, environment) {
    const isUat = environment === 'uat';
    const isDaily = checklistType === 'daily';
    
    // 如果是大版本割接计划且环境是UAT，则不包含发布清单
    const includeDeploy = !(checklistType === 'major' && isUat);
    
    // 显示加载中
    const resultDiv = document.getElementById('result');
    const loadingDiv = document.createElement('div');
    loadingDiv.className = 'loading';
    loadingDiv.textContent = '生成Markdown文件中...';
    resultDiv.appendChild(loadingDiv);
    
    // 获取真实数据
//    getReleaseData(versionName, environment);
}

// 初始化Tab切换功能
function initTabs() {
    const tabs = document.querySelectorAll('.tab');
    const tabContents = document.querySelectorAll('.tab-content');
    
    tabs.forEach(tab => {
        tab.addEventListener('click', () => {
            // 移除所有active类
            tabs.forEach(t => t.classList.remove('active'));
            tabContents.forEach(content => content.classList.remove('active'));
            
            // 添加active类到当前tab
            tab.classList.add('active');
            
            // 显示对应的内容
            const tabId = tab.getAttribute('data-tab');
            document.getElementById(`${tabId}-tab`).classList.add('active');
            
            // 清除发布清单容器
            const releaseListContainer = document.getElementById('releaseListContainer');
            if (releaseListContainer) {
                releaseListContainer.remove();
            }
            
            // 清除选中的radio按钮
            document.querySelectorAll('input[type="radio"]').forEach(radio => {
                radio.checked = false;
            });
        });
    });
}

// 初始化发布清单中的Tab功能
function initReleaseListTabs(container) {
    if (!container) return;
    
    const tabs = container.querySelectorAll('.tab');
    const tabContents = container.querySelectorAll('.tab-content');
    
    tabs.forEach(tab => {
        tab.addEventListener('click', () => {
            // 移除所有active类
            tabs.forEach(t => t.classList.remove('active'));
            tabContents.forEach(content => content.classList.remove('active'));
            
            // 添加active类到当前tab
            tab.classList.add('active');
            
            // 显示对应的内容
            const tabId = tab.getAttribute('data-tab');
            container.querySelector(`#${tabId}-tab`).classList.add('active');
            
            // 如果点击的是"检查节点"tab，自动开始检查
            if (tabId === 'check-nodes') {
                const versionName = document.getElementById('versionSearch').value;
                if (versionName) {
                    startBranchAndMergeCheck(versionName);
                }
            }
            
            // 如果点击的是"Release清单"tab，刷新数据
            if (tabId === 'release-list') {
                const versionName = document.getElementById('versionSearch').value;
                const environment = document.querySelector('input[name="dailyEnvironment"]:checked')?.value || 
                                   document.querySelector('input[name="majorEnvironment"]:checked')?.value;
                
                if (versionName && environment) {
                    // 显示加载中
                    const releaseListTab = container.querySelector('#release-list-tab');
                    if (releaseListTab) {
                        releaseListTab.innerHTML = '<div class="loading">加载最新数据中...</div>';
                    }
                    
                    // 重新获取数据并更新UI
                    refreshReleaseList(versionName, environment, container);
                }
            }
        });
    });
}

// 刷新Release清单数据
async function refreshReleaseList(versionName, environment, container) {
    try {
        // 从服务端获取最新数据
        const releaseData = await getReleaseData(versionName, environment);
        
        // 按类型分组
        const groupedByType = {
            'app': [],
            'db': [],
            'nacos': []
        };
        
        releaseData.forEach(item => {
            const type = item.releaseType || 'app';
            if (groupedByType[type]) {
                groupedByType[type].push(item);
            } else {
                groupedByType.app.push(item);
            }
        });
        
        // 类型标签映射
        const typeLabels = {
            'app': '应用发布',
            'db': '数据库发布',
            'nacos': '配置发布'
        };
        
        // 生成HTML
        let html = '';
        
        // 按类型生成部署区域
        Object.keys(typeLabels).forEach(type => {
            if (groupedByType[type] && groupedByType[type].length > 0) {
                html += generateDeploymentSectionFromData(typeLabels[type], groupedByType[type]);
            }
        });
        
        // 更新UI
        const releaseListTab = container.querySelector('#release-list-tab');
        if (releaseListTab) {
            releaseListTab.innerHTML = html || '<div class="info">没有发布清单数据</div>';
        }
    } catch (error) {
        console.error('刷新Release清单失败：', error);
        const releaseListTab = container.querySelector('#release-list-tab');
        if (releaseListTab) {
            releaseListTab.innerHTML = `<div class="error">获取最新数据失败: ${error.message}</div>`;
        }
    }
}

// 初始化事件监听
document.addEventListener('DOMContentLoaded', () => {
    loadReleaseVersions();

    const searchInput = document.getElementById('versionSearch');
    const optionsContainer = document.getElementById('versionOptions');
    const resultDiv = document.getElementById('result');

    // 添加展开/收起按钮的事件委托
    resultDiv.addEventListener('click', (e) => {
        if (e.target.classList.contains('toggle-btn')) {
            toggleSection(e.target);
        }
    });

    searchInput.addEventListener('input', (e) => {
        const searchText = e.target.value;
        filterVersions(searchText);
        optionsContainer.style.display = 'block';
    });

    searchInput.addEventListener('focus', () => {
        optionsContainer.style.display = 'block';
    });

    document.addEventListener('click', (e) => {
        if (!e.target.closest('.select-container')) {
            optionsContainer.style.display = 'none';
        }
    });

    document.getElementById('releaseForm').addEventListener('submit', async (event) => {
        event.preventDefault();

        const version = document.getElementById('versionSearch').value;

        if (!version) {
            resultDiv.innerHTML = '<div class="error">请选择版本号</div>';
            return;
        }

        try {
            resultDiv.innerHTML = '<div class="loading">查询中...</div>';

            // 清除之前的缓存，确保获取最新数据
            clearReleaseCache(version);

            const result = await queryRelease(version);
            resultDiv.innerHTML = formatResults(result);

            // 初始化Tab功能
            initTabs();

            // 异步加载历史版本信息
            loadHistoryVersions(version);
        } catch (error) {
            resultDiv.innerHTML = `<div class="error">${error.message}</div>`;
        }
    });
    
    // 添加环境选择事件委托
    document.addEventListener('change', function(e) {
        if (e.target && (e.target.name === 'dailyEnvironment' || e.target.name === 'majorEnvironment')) {
            const selectedEnv = e.target.value;
            const versionName = document.getElementById('versionSearch').value;
            
            if (versionName) {
                generateReleaseList(versionName, selectedEnv);
            } else {
                alert('请先选择一个版本号');
                e.target.checked = false;
            }
        }
    });
});

// 生成版本核对结果内容
function generateVersionCheckContent(releaseData) {
    // 这里需要从发布清单中获取issue和components信息
    // 我们需要访问已经查询到的Release信息
    
    // 获取当前查询的版本号
    const versionName = document.getElementById('versionSearch').value;
    
    // 从已查询的结果中获取issue和components信息
    return fetchIssueComponentsFromResult(versionName);
}

// 从已查询的结果中获取issue和components信息
async function fetchIssueComponentsFromResult(versionName) {
    try {
        // 查询Release信息
        const response = await queryRelease(versionName);
        
        if (!response || !response.data) {
            return '<tr><td colspan="5">未找到相关Release信息</td></tr>';
        }
        
        const { data } = response;
        
        // 加载app.json以获取component和gitName的映射
        const appDataResponse = await fetch('../json/app.json');
        const appData = await appDataResponse.json();
        
        // 加载jenkins.json以获取应用名称和Release GitLab名称的映射
        const jenkinsDataResponse = await fetch('../json/jenkins.json');
        const jenkinsData = await jenkinsDataResponse.json();
        
        // 获取当前环境
        const environment = document.querySelector('input[name="dailyEnvironment"]:checked')?.value || 
                           document.querySelector('input[name="majorEnvironment"]:checked')?.value || 
                           'uat';
        
        // 创建jenkins映射表 - 按环境和应用名称建立映射
        const jenkinsMap = {};
        jenkinsData.forEach(item => {
            const key = item.key;
            const value = item.value;
            
            // 解析key格式: ENV_TIC_AppName_Build
            const parts = key.split('_');
            if (parts.length >= 3) {
                const env = parts[0];
                const appName = parts.slice(2, parts.length - 1).join('_'); // 处理可能包含下划线的应用名称
                
                // 按环境分组存储
                if (!jenkinsMap[env]) {
                    jenkinsMap[env] = {};
                }
                
                // 存储应用名称到GitLab名称的映射
                jenkinsMap[env][appName.toLowerCase()] = value;
            }
        });
        
        // 获取应用发布tab页中的所有Issue和对应的应用名称
        const releaseIssues = {};
        
        try {
            const releaseData = await getReleaseData(versionName, environment);
            
            // 提取所有应用发布中的Issue和应用名称
            releaseData.filter(item => item.releaseType === 'app').forEach(item => {
                if (item.issue) {
                    if (!releaseIssues[item.issue]) {
                        releaseIssues[item.issue] = {
                            appNames: new Set(),
                            gitNames: new Set()
                        };
                    }
                    
                    if (item.appName) {
                        // 尝试从jenkins映射中获取GitLab名称
                        const envPrefix = environment.toUpperCase();
                        const appNameLower = item.appName.toLowerCase();
                        
                        // 查找映射
                        if (jenkinsMap[envPrefix] && jenkinsMap[envPrefix][appNameLower]) {
                            releaseIssues[item.issue].gitNames.add(jenkinsMap[envPrefix][appNameLower]);
                        } else {
                            // 如果没有直接匹配，尝试部分匹配
                            let found = false;
                            
                            if (jenkinsMap[envPrefix]) {
                                // 遍历所有键，查找包含当前appName的键
                                Object.keys(jenkinsMap[envPrefix]).forEach(key => {
                                    if (key.includes(appNameLower) || appNameLower.includes(key)) {
                                        releaseIssues[item.issue].gitNames.add(jenkinsMap[envPrefix][key]);
                                        found = true;
                                    }
                                });
                            }
                            
                            // 如果仍未找到，保存原始appName
                            if (!found) {
                                releaseIssues[item.issue].appNames.add(item.appName);
                            }
                        }
                    }
                    
                    // 如果已有gitName，直接添加
                    if (item.gitName) {
                        releaseIssues[item.issue].gitNames.add(item.gitName);
                    }
                }
            });
        } catch (error) {
            console.error('获取发布清单数据失败：', error);
        }
        
        // 按Issue合并components和gitName
        const mergedIssues = {};
        
        // 收集所有issues的components
        ['Story', 'Task', 'Bug'].forEach(type => {
            if (data[type] && data[type].length > 0) {
                data[type].forEach(issue => {
                    const issueKey = issue.key || '-';
                    
                    if (!mergedIssues[issueKey]) {
                        mergedIssues[issueKey] = {
                            components: new Set(),
                            gitNames: new Set(),
                            releaseGitNames: new Set()
                        };
                    }
                    
                    // 添加components
                    if (issue.components && Array.isArray(issue.components)) {
                        issue.components.forEach(comp => {
                            mergedIssues[issueKey].components.add(comp);
                            
                            // 查找匹配的gitName
                            const matchedApp = appData.find(app => app.component === comp);
                            if (matchedApp && matchedApp.gitName) {
                                mergedIssues[issueKey].gitNames.add(matchedApp.gitName);
                            }
                        });
                    }
                    
                    // 添加releaseGitNames
                    if (releaseIssues[issueKey]) {
                        // 直接使用转换后的gitNames
                        releaseIssues[issueKey].gitNames.forEach(gitName => {
                            mergedIssues[issueKey].releaseGitNames.add(gitName);
                        });
                        
                        // 如果还有未转换的appNames，尝试再次转换
                        releaseIssues[issueKey].appNames.forEach(appName => {
                            const envPrefix = environment.toUpperCase();
                            const appNameLower = appName.toLowerCase();
                            
                            // 尝试从jenkins映射中获取GitLab名称
                            if (jenkinsMap[envPrefix] && jenkinsMap[envPrefix][appNameLower]) {
                                mergedIssues[issueKey].releaseGitNames.add(jenkinsMap[envPrefix][appNameLower]);
                            } else {
                                // 如果没有找到匹配项，使用原始appName
                                mergedIssues[issueKey].releaseGitNames.add(appName);
                            }
                        });
                    }
                });
            }
        });
        
        // 生成表格内容
        let html = '';
        
        // 将合并后的数据转换为数组并排序
        const sortedIssues = Object.keys(mergedIssues).sort();
        
        sortedIssues.forEach(issueKey => {
            const issue = mergedIssues[issueKey];
            
            // 将Set转换为排序后的字符串
            const components = Array.from(issue.components).sort().join(', ');
            const gitlabNames = Array.from(issue.gitNames).sort().join(', ');
            const releaseGitlabNames = Array.from(issue.releaseGitNames).sort().join(', ');
            
            // 完全比对GitLab名称和Release GitLab名称
            let isMatch = false;
            
            if (gitlabNames && releaseGitlabNames) {
                const gitNameArray = Array.from(issue.gitNames).sort();
                const releaseGitNameArray = Array.from(issue.releaseGitNames).sort();
                
                // 检查gitNameArray中的每个元素是否都在releaseGitNameArray中
                isMatch = gitNameArray.every(name => 
                    releaseGitNameArray.some(releaseName => 
                        releaseName.toLowerCase() === name.toLowerCase()
                    )
                );
            } else {
                isMatch = (gitlabNames === '' && releaseGitlabNames === '');
            }
            
            const compareResult = isMatch ? 
                '<span class="status-badge status-success">一致</span>' : 
                '<span class="status-badge status-error">不一致</span>';
            
            html += `
                <tr>
                    <td>${issueKey}</td>
                    <td>${components}</td>
                    <td>${gitlabNames}</td>
                    <td>${releaseGitlabNames}</td>
                    <td>${compareResult}</td>
                </tr>
            `;
        });
        
        return html || '<tr><td colspan="5">暂无版本核对数据</td></tr>';
    } catch (error) {
        console.error('获取issue和components信息失败：', error);
        return `<tr><td colspan="5">获取数据失败: ${error.message}</td></tr>`;
    }
}

// 添加分支信息的CSS样式
document.addEventListener('DOMContentLoaded', () => {
    // 添加已有的事件监听...

    // 添加分支信息样式
    const style = document.createElement('style');
    style.textContent = `
        .branch-info {
            margin-top: 20px;
            padding-top: 15px;
            border-top: 1px solid #eee;
        }

        .branch-info h4 {
            margin: 0 0 10px 0;
            color: #555;
            font-size: 14px;
            display: flex;
            align-items: center;
        }

        .component-branch {
            margin-bottom: 15px;
        }

        .component-branch h5 {
            margin: 0 0 8px 0;
            color: #666;
            font-size: 13px;
            font-weight: bold;
        }

        .component-branch ul {
            margin: 0;
            padding-left: 20px;
            list-style-type: none;
        }

        .component-branch li {
            margin: 3px 0;
            color: #444;
            padding: 3px 8px;
            background: #f8f8f8;
            border-radius: 3px;
            font-size: 12px;
            border-left: 3px solid #4CAF50;
        }

        .component-branch p {
            margin: 5px 0;
            color: #999;
            font-style: italic;
            font-size: 12px;
        }
    `;
    document.head.appendChild(style);
});

// 切换检查节点展开/收起
function toggleCheckNode(element) {
    const content = element.nextElementSibling;
    const toggleIcon = element.querySelector('.toggle-icon');
    
    if (content.style.display === 'none') {
        content.style.display = 'block';
        toggleIcon.textContent = '-';
    } else {
        content.style.display = 'none';
        toggleIcon.textContent = '+';
    }
}

// 更新检查节点状态
function updateNodeStatus(nodeName, status, message) {
    console.log(`更新节点状态: ${nodeName}, ${status}, ${message}`);
    
    // 查找对应的节点
    const nodes = document.querySelectorAll('.check-node');
    let targetNode = null;
    
    for (const node of nodes) {
        const titleElement = node.querySelector('.node-title');
        if (titleElement && titleElement.textContent === nodeName) {
            targetNode = node;
            break;
        }
    }
    
    if (!targetNode) {
        console.error(`未找到节点: ${nodeName}`);
        return;
    }
    
    // 更新状态
    const statusElement = targetNode.querySelector('.node-result .status-badge');
    if (statusElement) {
        // 移除所有状态类
        statusElement.classList.remove('status-pending', 'status-success', 'status-error', 'status-warning');
        
        // 添加新的状态类
        statusElement.classList.add(`status-${status}`);
        
        // 更新文本
        statusElement.textContent = message;
    }
}

// 开始所有检查
async function startAllChecks(versionName) {
    if (!versionName) {
        alert('请先选择一个版本号');
        return;
    }
    
    // 获取当前环境
    const dailyEnvUat = document.getElementById('dailyEnvUat');
    const dailyEnvProd = document.getElementById('dailyEnvProd');
    const environment = (dailyEnvUat && dailyEnvUat.checked) ? 'UAT' :
                       (dailyEnvProd && dailyEnvProd.checked) ? 'prod' : 'UAT';
    
    try {
        // 调用服务端接口获取分支信息（只调用一次）
        const response = await fetch('http://10.169.128.35:8611/business/api/v1/release/release/getReleaseBranch', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                releaseName: versionName,
                env: environment
            })
        });

        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        const result = await response.json();
        console.log('获取到分支数据:', result);
        
        if (result.resultCode !== "0") {
            throw new Error(`API error: ${result.resultMsg}`);
        }
        
        // 使用同一份数据进行两种检查
        await processBranchCheck(result.data);
        await processMergeCheck(result.data, environment);
        
        console.log('所有检查已完成');
    } catch (error) {
        console.error('检查失败:', error);
        // 更新两个检查节点的状态
        updateNodeStatus('分支检查', 'error', '检查失败');
        updateNodeStatus('分支合并检查', 'error', '检查失败');
    }
}

// 处理分支检查（使用已获取的数据）
async function processBranchCheck(data) {
    const resultContainer = document.getElementById('branch-check-result');
    if (!resultContainer) return;
    
    // 显示加载中
    resultContainer.innerHTML = '<tr><td colspan="3">处理中...</td></tr>';
    
    // 更新节点状态
    updateNodeStatus('分支检查', 'pending', '检查中...');
    
    try {
        // 处理新的数据结构
        const branchData = data && data.projectMap ? data.projectMap : {};
        
        if (Object.keys(branchData).length === 0) {
            resultContainer.innerHTML = '<tr><td colspan="3">未找到分支信息</td></tr>';
            updateNodeStatus('分支检查', 'warning', '未找到分支信息');
            return;
        }
        
        // 生成分支检查结果HTML
        let html = '';
        let allSuccess = true;
        
        for (const [component, branches] of Object.entries(branchData)) {
            if (!branches || branches.length === 0) {
                html += `
                    <tr>
                        <td>${component}</td>
                        <td>-</td>
                        <td><span class="status-badge status-error">分支未找到</span></td>
                    </tr>
                `;
                allSuccess = false;
            } else {
                branches.forEach(branch => {
                    html += `
                        <tr>
                            <td>${component}</td>
                            <td>${branch}</td>
                            <td><span class="status-badge status-success">存在</span></td>
                        </tr>
                    `;
                });
            }
        }
        
        resultContainer.innerHTML = html;
        console.log('更新分支检查结果HTML完成');
        
        // 更新节点状态
        if (allSuccess) {
            updateNodeStatus('分支检查', 'success', '检查通过');
        } else {
            updateNodeStatus('分支检查', 'error', '检查不通过');
        }
    } catch (error) {
        console.error('分支检查处理失败:', error);
        resultContainer.innerHTML = `<tr><td colspan="3">处理失败: ${error.message}</td></tr>`;
        updateNodeStatus('分支检查', 'error', '检查失败');
    }
}

// 处理分支合并检查（使用已获取的数据）
async function processMergeCheck(data, environment) {
    const resultContainer = document.getElementById('merge-check-result');
    if (!resultContainer) return;
    
    // 显示加载中
    resultContainer.innerHTML = '<tr><td colspan="4">处理中...</td></tr>';
    
    // 更新节点状态
    updateNodeStatus('分支合并检查', 'pending', '检查中...');
    
    try {
        // 处理新的数据结构 - 使用branchMap
        const branchData = data && data.branchMap ? data.branchMap : {};
        
        if (Object.keys(branchData).length === 0) {
            resultContainer.innerHTML = '<tr><td colspan="4">未找到分支合并信息</td></tr>';
            updateNodeStatus('分支合并检查', 'warning', '未找到分支信息');
            return;
        }
        
        // 确定目标分支（根据环境）
        const targetBranch = environment === 'prod' ? 'prod' : 'UAT';
        
        // 生成分支合并检查结果HTML
        let html = '';
        let allSuccess = true;
        
        for (const [component, branches] of Object.entries(branchData)) {
            if (!branches || branches.length === 0) {
                html += `
                    <tr>
                        <td>${component}</td>
                        <td>-</td>
                        <td>${targetBranch}</td>
                        <td><span class="status-badge status-error">分支未找到</span></td>
                    </tr>
                `;
                allSuccess = false;
            } else {
                for (const branch of branches) {
                    // 使用接口返回的isMarge字段判断是否已合并
                    const branchName = branch.branchName || branch;
                    const isMerged = branch.isMarge === 1;
                    const status = isMerged ? 
                        '<span class="status-badge status-success">已合并</span>' : 
                        '<span class="status-badge status-error">未合并</span>';
                    
                    html += `
                        <tr>
                            <td>${component}</td>
                            <td>${branchName}</td>
                            <td>${targetBranch}</td>
                            <td>${status}</td>
                        </tr>
                    `;
                    
                    if (!isMerged) allSuccess = false;
                }
            }
        }
        
        resultContainer.innerHTML = html;
        
        // 更新节点状态
        if (allSuccess) {
            updateNodeStatus('分支合并检查', 'success', '检查通过');
        } else {
            updateNodeStatus('分支合并检查', 'error', '检查不通过');
        }
    } catch (error) {
        console.error('分支合并检查处理失败:', error);
        resultContainer.innerHTML = `<tr><td colspan="4">处理失败: ${error.message}</td></tr>`;
        updateNodeStatus('分支合并检查', 'error', '检查失败');
    }
}

// 开始分支检查 - 修改为调用统一接口
async function startBranchCheck(versionName) {
    if (!versionName) {
        alert('请先选择一个版本号');
        return;
    }
    
    // 获取当前环境
    const dailyEnvUat = document.getElementById('dailyEnvUat');
    const dailyEnvProd = document.getElementById('dailyEnvProd');
    const environment = (dailyEnvUat && dailyEnvUat.checked) ? 'UAT' :
                       (dailyEnvProd && dailyEnvProd.checked) ? 'prod' : 'UAT';
    
    try {
        // 调用服务端接口获取分支信息
        const response = await fetch('http://10.169.128.35:8611/business/api/v1/release/release/getReleaseBranch', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                releaseName: versionName,
                env: environment
            })
        });

        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        const result = await response.json();
        console.log('获取到分支数据:', result);
        
        if (result.resultCode !== "0") {
            throw new Error(`API error: ${result.resultMsg}`);
        }
        
        // 处理分支检查
        await processBranchCheck(result.data);
        
    } catch (error) {
        console.error('分支检查失败:', error);
        const resultContainer = document.getElementById('branch-check-result');
        if (resultContainer) {
            resultContainer.innerHTML = `<tr><td colspan="3">检查失败: ${error.message}</td></tr>`;
        }
        updateNodeStatus('分支检查', 'error', '检查失败');
    }
}

// 开始分支合并检查 - 修改为调用统一接口
async function startMergeCheck(versionName) {
    if (!versionName) {
        alert('请先选择一个版本号');
        return;
    }
    
    // 获取当前环境
    const dailyEnvUat = document.getElementById('dailyEnvUat');
    const dailyEnvProd = document.getElementById('dailyEnvProd');
    const environment = (dailyEnvUat && dailyEnvUat.checked) ? 'UAT' :
                       (dailyEnvProd && dailyEnvProd.checked) ? 'prod' : 'UAT';
    
    try {
        // 调用服务端接口获取分支信息
        const response = await fetch('http://10.169.128.35:8611/business/api/v1/release/release/getReleaseBranch', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                releaseName: versionName,
                env: environment
            })
        });

        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        const result = await response.json();
        
        if (result.resultCode !== "0") {
            throw new Error(`API error: ${result.resultMsg}`);
        }
        
        // 处理分支合并检查
        await processMergeCheck(result.data, environment);
        
    } catch (error) {
        console.error('分支合并检查失败:', error);
        const resultContainer = document.getElementById('merge-check-result');
        if (resultContainer) {
            resultContainer.innerHTML = `<tr><td colspan="4">检查失败: ${error.message}</td></tr>`;
        }
        updateNodeStatus('分支合并检查', 'error', '检查失败');
    }
}

// 新增函数：同时开始分支检查和合并检查
async function startBranchAndMergeCheck(versionName) {
    if (!versionName) {
        console.error('版本号不能为空');
        alert('版本号不能为空，请先选择一个版本');
        return;
    }
    
    console.log('开始检查，版本号:', versionName);
    
    // 获取当前环境
    const environment = document.querySelector('input[name="dailyEnvironment"]:checked')?.value || 
                       document.querySelector('input[name="majorEnvironment"]:checked')?.value || 
                       'uat';
    
    if (!environment) {
        alert('请先选择环境');
        return;
    }
    
    // 更新状态为检查中
    updateNodeStatus('分支检查', 'pending', '检查中...');
    updateNodeStatus('分支合并检查', 'pending', '检查中...');
    
    // 更新表格内容为"检查中..."
    const branchResultContainer = document.getElementById('branch-check-result');
    if (branchResultContainer) {
        branchResultContainer.innerHTML = '<tr><td colspan="3">检查中...</td></tr>';
    }
    
    const mergeResultContainer = document.getElementById('merge-check-result');
    if (mergeResultContainer) {
        mergeResultContainer.innerHTML = '<tr><td colspan="4">检查中...</td></tr>';
    }
    
    try {
        // 调用服务端接口获取分支信息
        const response = await fetch('http://10.169.128.35:8611/business/api/v1/release/release/getReleaseBranch', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                releaseName: versionName,
                env: environment
            })
        });

        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        const result = await response.json();
        console.log('获取到分支数据:', result);
        
        if (result.resultCode !== "0") {
            throw new Error(`API error: ${result.resultMsg}`);
        }
        
        // 处理分支检查和合并检查
        await processBranchCheck(result.data);
        await processMergeCheck(result.data, environment);
        
        console.log('检查完成');
        
    } catch (error) {
        console.error('检查失败:', error);
        // 更新两个检查节点的状态
        updateNodeStatus('分支检查', 'error', '检查失败');
        updateNodeStatus('分支合并检查', 'error', '检查失败');
        
        // 更新表格内容
        const branchResultContainer = document.getElementById('branch-check-result');
        if (branchResultContainer) {
            branchResultContainer.innerHTML = `<tr><td colspan="3">检查失败: ${error.message}</td></tr>`;
        }
        
        const mergeResultContainer = document.getElementById('merge-check-result');
        if (mergeResultContainer) {
            mergeResultContainer.innerHTML = `<tr><td colspan="4">检查失败: ${error.message}</td></tr>`;
        }
        
        alert(`检查失败: ${error.message}`);
    }
}

// 添加检查节点样式
document.addEventListener('DOMContentLoaded', () => {
    // 添加已有的事件监听...
    
    // 添加检查节点样式
    const style = document.createElement('style');
    style.textContent += `
        .check-nodes-container {
            padding: 15px;
        }
        
        .check-nodes-actions {
            display: flex;
            gap: 10px;
            margin-bottom: 20px;
            padding: 10px;
            background-color: #f8f8f8;
            border-radius: 6px;
            border: 1px solid #eee;
        }
        
        .action-btn {
            padding: 8px 16px;
            background-color: #f0f0f0;
            color: #333;
            border: 1px solid #ddd;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.2s;
        }
        
        .action-btn:hover {
            background-color: #e0e0e0;
        }
        
        .action-btn.primary-btn {
            background-color: #4CAF50;
            color: white;
            border: none;
        }
        
        .action-btn.primary-btn:hover {
            background-color: #45a049;
        }
        
        .check-node {
            margin-bottom: 20px;
            border: 1px solid #ddd;
            border-radius: 6px;
            overflow: hidden;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        }
        
        .check-node-header {
            display: grid;
            grid-template-columns: 120px 1fr 120px;
            align-items: center;
            padding: 12px 15px;
            background-color: #f8f8f8;
        }
        
        .node-title {
            font-weight: bold;
            color: #333;
        }
        
        .node-info {
            color: #666;
            font-size: 13px;
        }
        
        .node-result {
            text-align: center;
        }
        
        .check-node-content {
            padding: 15px;
            background-color: #fff;
        }
        
        .node-detail-container {
            width: 100%;
            overflow-x: auto;
        }
        
        .node-detail-container table {
            width: 100%;
            border-collapse: collapse;
        }
        
        .node-detail-container th,
        .node-detail-container td {
            padding: 8px 12px;
            text-align: left;
            border-bottom: 1px solid #eee;
        }
        
        .node-detail-container th {
            background-color: #f8f8f8;
            font-weight: bold;
        }
        
        .status-badge {
            display: inline-block;
            padding: 3px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: bold;
        }
        
        .status-success {
            background-color: #e6f7e6;
            color: #2e7d32;
        }
        
        .status-error {
            background-color: #fdecea;
            color: #d32f2f;
        }
        
        .status-warning {
            background-color: #fff8e1;
            color: #ff8f00;
        }
        
        .status-pending {
            background-color: #e3f2fd;
            color: #1976d2;
        }
        
        /* 调整表格列宽 - 更严格地限制组件列宽度 */
        .node-detail-container table {
            table-layout: fixed; /* 使用固定表格布局，确保列宽严格遵循设置 */
            width: 100%;
        }
        
        /* 组件列固定宽度为50px，确保只显示约50个字符 */
        .node-detail-container table th:first-child,
        .node-detail-container table td:first-child {
            width: 200px !important; /* 使用!important确保优先级 */
            max-width: 200px !important;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }
        
        /* 分支名称列获取剩余所有宽度 */
        .node-detail-container table th:nth-child(2),
        .node-detail-container table td:nth-child(2) {
            width: auto; /* 自动占用剩余空间 */
            min-width: 300px;
            white-space: normal;
            word-break: break-all;
        }
        
        /* 检查结果列宽度固定 */
        .node-detail-container table th:nth-child(3),
        .node-detail-container table td:nth-child(3) {
            width: 100px;
            max-width: 100px;
            text-align: center;
        }
        
        /* 合并检查表格的目标分支列宽度固定 */
        #merge-check-result th:nth-child(3),
        #merge-check-result td:nth-child(3) {
            width: 100px;
            max-width: 100px;
            text-align: center;
        }
        
        /* 合并检查表格的合并状态列宽度固定 */
        #merge-check-result th:nth-child(4),
        #merge-check-result td:nth-child(4) {
            width: 100px;
            max-width: 100px;
            text-align: center;
        }
        
        /* 添加表格单元格的样式 */
        .node-detail-container td {
            padding: 8px 12px;
            vertical-align: middle;
        }
        
        /* 表格悬停效果 */
        .node-detail-container tr:hover {
            background-color: #f5f5f5;
        }
        
        /* 表格内容溢出处理 */
        .node-detail-container {
            overflow-x: auto;
        }
    `;
    document.head.appendChild(style);
});

// 清除缓存（当需要强制刷新数据时使用）
function clearReleaseCache(releaseName) {
    if (releaseName) {
        delete releaseCache[releaseName];
    } else {
        // 清除所有缓存
        Object.keys(releaseCache).forEach(key => delete releaseCache[key]);
    }
}

// 根据状态值返回对应的CSS类名
function getStatusClass(status) {
    if (!status) return 'status-pending';
    
    switch(status.toLowerCase()) {
        case 'success':
        case '成功':
        case '已发布':
            return 'status-success';
        case 'fail':
        case 'failed':
        case '失败':
            return 'status-error';
        case 'pending':
        case '待发布':
            return 'status-pending';
        case 'processing':
        case '发布中':
            return 'status-warning';
        default:
            return 'status-pending';
    }
}
