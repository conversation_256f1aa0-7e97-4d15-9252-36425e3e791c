<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <title>Release发布流程</title>
    <style>
        body {
            margin: 0;
            padding: 15px;
            font-family: Arial, sans-serif;
            font-size: 14px;
            background-color: #f5f5f5;
        }

        h2 {
            margin: 0 0 20px 0;
            font-size: 18px;
            color: #333;
            text-align: center;
        }

        /* 表头筛选区域样式 */
        .header-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }

        .form-container {
            display: flex;
            align-items: center;
            gap: 15px;
            margin-bottom: 20px;
        }

        .form-group {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        label {
            white-space: nowrap;
            font-weight: bold;
            color: #666;
        }

        .select-container {
            position: relative;
            width: 200px;
        }

        .search-input {
            width: 100%;
            padding: 8px;
            box-sizing: border-box;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }

        .search-input:focus {
            border-color: #1e88e5;
            outline: none;
        }

        button {
            padding: 8px 20px;
            background-color: #4CAF50;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            white-space: nowrap;
        }

        button:hover {
            background-color: #45a049;
        }

        .select-options {
            position: absolute;
            top: 100%;
            left: 0;
            right: 0;
            max-height: 200px;
            overflow-y: auto;
            background: white;
            border: 1px solid #ddd;
            border-radius: 4px;
            margin-top: 4px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            z-index: 1000;
            display: none;
        }

        .select-option {
            padding: 8px;
            cursor: pointer;
        }

        .select-option:hover {
            background-color: #f5f5f5;
        }

        /* 历史版本区域样式 */
        .history-section {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }

        .section-title {
            margin: 0 0 15px 0;
            font-size: 16px;
            color: #333;
            font-weight: bold;
        }

        .history-versions-list {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 10px;
            margin-top: 10px;
        }

        .history-version-item {
            display: flex;
            align-items: center;
            padding: 12px 15px;
            background: #f9f9f9;
            border: 1px solid #eee;
            border-radius: 4px;
            transition: background-color 0.2s;
        }

        .history-version-item:hover {
            background-color: #f0f0f0;
        }

        .version-index {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            width: 24px;
            height: 24px;
            background: #4CAF50;
            color: white;
            border-radius: 50%;
            font-size: 12px;
            font-weight: bold;
            margin-right: 12px;
            flex-shrink: 0;
        }

        .version-name {
            flex: 1;
            font-size: 14px;
            color: #333;
            word-break: break-word;
        }

        /* 流程灯样式 */
        .process-flow {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }

        .flow-steps {
            display: flex;
            justify-content: space-between;
            align-items: center;
            position: relative;
            margin: 20px 0;
        }

        .flow-step {
            display: flex;
            flex-direction: column;
            align-items: center;
            position: relative;
            flex: 1;
            z-index: 2;
        }

        .step-circle {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: #e0e0e0;
            color: #666;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            font-size: 14px;
            margin-bottom: 8px;
            transition: all 0.3s ease;
        }

        .step-circle.active {
            background: #4CAF50;
            color: white;
        }

        .step-circle.completed {
            background: #2196F3;
            color: white;
        }

        .step-label {
            font-size: 12px;
            color: #666;
            text-align: center;
            max-width: 80px;
            line-height: 1.2;
        }

        .flow-line {
            position: absolute;
            top: 20px;
            left: 0;
            right: 0;
            height: 2px;
            background: #e0e0e0;
            z-index: 1;
        }

        .flow-line-progress {
            height: 100%;
            background: #4CAF50;
            transition: width 0.3s ease;
            width: 0%;
        }

        /* Tab样式 */
        .tabs-container {
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .tabs {
            display: flex;
            border-bottom: 1px solid #ddd;
            background: #f9f9f9;
        }

        .tab {
            padding: 15px 20px;
            cursor: pointer;
            border: none;
            background: transparent;
            font-size: 14px;
            color: #666;
            transition: all 0.3s;
            border-bottom: 3px solid transparent;
        }

        .tab:hover {
            background-color: #f0f0f0;
            color: #333;
        }

        .tab.active {
            background-color: white;
            color: #4CAF50;
            border-bottom-color: #4CAF50;
            font-weight: bold;
        }

        .tab-content {
            display: none;
            padding: 20px;
            min-height: 400px;
        }

        .tab-content.active {
            display: block;
        }

        .loading {
            text-align: center;
            padding: 40px;
            color: #666;
            font-size: 16px;
        }

        .error {
            color: #f44336;
            padding: 15px;
            background-color: #ffebee;
            border-radius: 4px;
            margin: 10px 0;
        }

        .info {
            text-align: center;
            padding: 40px;
            color: #666;
            font-style: italic;
        }

        /* 分支检查区域样式 */
        .branch-check-section h4 {
            margin: 0 0 15px 0;
            color: #333;
            font-size: 16px;
            font-weight: bold;
        }

        .branch-check-section h5 {
            margin: 0 0 10px 0;
            color: #333;
            font-size: 16px;
            font-weight: bold;
            border-bottom: 1px solid #eee;
            padding-bottom: 8px;
        }

        .branch-check-section table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 10px;
        }

        .branch-check-section th,
        .branch-check-section td {
            padding: 8px 12px;
            text-align: left;
            border: 1px solid #ddd;
        }

        .branch-check-section th {
            background-color: #f5f5f5;
            font-weight: bold;
        }

        .branch-check-section .project-container {
            margin-bottom: 20px;
            border: 1px solid #eee;
            border-radius: 4px;
            padding: 15px;
            background: #fafafa;
        }

        .branch-check-section .no-branches {
            padding: 20px;
            text-align: center;
            color: #666;
            background-color: #f9f9f9;
            border-radius: 4px;
            font-style: italic;
        }

        /* 分支表格样式 */
        .branch-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 10px;
            table-layout: fixed;
        }

        .branch-table th,
        .branch-table td {
            padding: 8px 12px;
            text-align: left;
            border: 1px solid #ddd;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }

        .branch-table th {
            background-color: #f5f5f5;
            font-weight: bold;
        }

        /* 分支名称列 - 自适应宽度 */
        .branch-name-col {
            width: calc(100% - 120px); /* 总宽度减去状态列宽度 */
        }

        /* 状态列 - 固定100字符宽度 */
        .branch-status-col {
            width: 120px;
            min-width: 120px;
            max-width: 120px;
        }

        /* 状态样式 */
        .status-success {
            color: #4CAF50;
            font-weight: bold;
            display: inline-flex;
            align-items: center;
            gap: 4px;
        }

        .status-warning {
            color: #FF9800;
            font-weight: bold;
            display: inline-flex;
            align-items: center;
            gap: 4px;
        }

        .status-error {
            color: #f44336;
            font-weight: bold;
            display: inline-flex;
            align-items: center;
            gap: 4px;
        }

        /* 分支不存在警告样式 */
        .no-branches-warning {
            display: flex;
            align-items: flex-start;
            gap: 12px;
            padding: 16px;
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 4px;
            margin-top: 10px;
        }

        .warning-icon {
            font-size: 20px;
            flex-shrink: 0;
        }

        .warning-text {
            flex: 1;
        }

        .warning-text strong {
            color: #856404;
            font-size: 14px;
            display: block;
            margin-bottom: 4px;
        }

        .warning-text p {
            color: #856404;
            font-size: 13px;
            margin: 0;
            line-height: 1.4;
        }

        /* Issue区域样式 */
        .issue-section {
            margin-bottom: 30px;
            border: 1px solid #eee;
            border-radius: 6px;
            overflow: hidden;
        }

        .issue-section h4 {
            margin: 0;
            padding: 15px;
            background-color: #f5f5f5;
            border-bottom: 1px solid #eee;
            font-size: 16px;
            color: #333;
        }

        .issue-section table {
            width: 100%;
            border-collapse: collapse;
            table-layout: fixed;
        }

        .issue-section th,
        .issue-section td {
            padding: 8px 12px;
            text-align: left;
            border: 1px solid #ddd;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }

        .issue-section th {
            background-color: #f9f9f9;
            font-weight: bold;
        }

        /* 设置各列的固定宽度百分比 */
        .issue-section th:nth-child(1),
        .issue-section td:nth-child(1) {
            width: 12%; /* Issue列 - 最多20个字符 */
        }

        .issue-section th:nth-child(2),
        .issue-section td:nth-child(2) {
            width: 40%; /* Title列 - 剩余空间 */
        }

        .issue-section th:nth-child(3),
        .issue-section td:nth-child(3) {
            width: 15%; /* Assignee列 - 最多100字符 */
        }

        .issue-section th:nth-child(4),
        .issue-section td:nth-child(4) {
            width: 25%; /* Components列 - 最多300字符 */
        }

        .issue-section th:nth-child(5),
        .issue-section td:nth-child(5) {
            width: 8%; /* Status列 - 最多20个字符 */
        }

        /* Title列允许换行，其他列保持单行 */
        .issue-section td:nth-child(2) {
            white-space: normal;
            word-break: break-word;
            line-height: 1.4;
        }

        /* 为超出内容添加tooltip效果 */
        .issue-section td[title] {
            cursor: help;
        }

        .issue-section a {
            color: #1976d2;
            text-decoration: none;
            display: block;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        .issue-section a:hover {
            text-decoration: underline;
        }

        /* 确保链接在Title列中正确显示 */
        .issue-section td:nth-child(2) a {
            white-space: normal;
            word-break: break-word;
            display: inline;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .form-container {
                flex-direction: column;
                align-items: stretch;
            }
            
            .flow-steps {
                flex-wrap: wrap;
                gap: 20px;
            }
            
            .flow-step {
                flex: 0 0 calc(33.333% - 20px);
            }
            
            .tabs {
                flex-wrap: wrap;
            }
            
            .tab {
                flex: 1;
                min-width: 120px;
            }
        }
    </style>
</head>
<body>
    <h2>Release发布流程</h2>
    
    <!-- 表头筛选区域 -->
    <div class="header-container">
        <form id="releaseForm" class="form-container">
            <div class="form-group">
                <label for="versionSearch">Release版本号:</label>
                <div class="select-container">
                    <input type="text" id="versionSearch" class="search-input" placeholder="搜索版本号...">
                    <div id="versionOptions" class="select-options"></div>
                </div>
            </div>
            <button type="submit">查询</button>
        </form>
    </div>

    <!-- 历史版本区域 -->
    <div class="history-section">
        <h3 class="section-title">历史release版本</h3>
        <div class="loading" id="history-versions-loading" style="display: none;">加载历史版本中...</div>
        <div class="history-versions-content" id="history-versions-content">
            <div class="info">请先选择一个版本号</div>
        </div>
    </div>

    <!-- 流程灯区域 -->
    <div class="process-flow">
        <h3 class="section-title">发布流程</h3>
        <div class="flow-steps">
            <div class="flow-line">
                <div class="flow-line-progress" id="flowProgress"></div>
            </div>
            <div class="flow-step" data-step="1">
                <div class="step-circle">1</div>
                <div class="step-label">开始</div>
            </div>
            <div class="flow-step" data-step="2">
                <div class="step-circle">2</div>
                <div class="step-label">需求范围确认</div>
            </div>
            <div class="flow-step" data-step="3">
                <div class="step-circle">3</div>
                <div class="step-label">开发中</div>
            </div>
            <div class="flow-step" data-step="4">
                <div class="step-circle">4</div>
                <div class="step-label">代码扫描</div>
            </div>
            <div class="flow-step" data-step="5">
                <div class="step-circle">5</div>
                <div class="step-label">Test环境发布</div>
            </div>
            <div class="flow-step" data-step="6">
                <div class="step-circle">6</div>
                <div class="step-label">UAT环境发布</div>
            </div>
            <div class="flow-step" data-step="7">
                <div class="step-circle">7</div>
                <div class="step-label">上线发布</div>
            </div>
            <div class="flow-step" data-step="8">
                <div class="step-circle">8</div>
                <div class="step-label">发布检查</div>
            </div>
            <div class="flow-step" data-step="9">
                <div class="step-circle">9</div>
                <div class="step-label">结束</div>
            </div>
        </div>
    </div>

    <!-- Tab窗口区域 -->
    <div class="tabs-container">
        <div class="tabs">
            <button class="tab active" data-tab="requirements">需求/功能Issue</button>
            <button class="tab" data-tab="development">开发</button>
            <button class="tab" data-tab="code-scan">代码扫描</button>
            <button class="tab" data-tab="test-deploy">Test环境发布</button>
            <button class="tab" data-tab="uat-deploy">UAT环境发布</button>
            <button class="tab" data-tab="prod-deploy">上线发布</button>
            <button class="tab" data-tab="release-check">发布检查</button>
        </div>

        <!-- 需求/功能Issue Tab -->
        <div class="tab-content active" id="requirements-content">
            <div class="loading" id="requirements-loading">请先选择一个版本号</div>
        </div>

        <!-- 开发 Tab -->
        <div class="tab-content" id="development-content">
            <div class="loading" id="development-loading">请先选择一个版本号</div>
        </div>

        <!-- 代码扫描 Tab -->
        <div class="tab-content" id="code-scan-content">
            <div class="info">代码扫描功能开发中...</div>
        </div>

        <!-- Test环境发布 Tab -->
        <div class="tab-content" id="test-deploy-content">
            <div class="info">Test环境发布功能开发中...</div>
        </div>

        <!-- UAT环境发布 Tab -->
        <div class="tab-content" id="uat-deploy-content">
            <div class="info">UAT环境发布功能开发中...</div>
        </div>

        <!-- 上线发布 Tab -->
        <div class="tab-content" id="prod-deploy-content">
            <div class="info">上线发布功能开发中...</div>
        </div>

        <!-- 发布检查 Tab -->
        <div class="tab-content" id="release-check-content">
            <div class="info">发布检查功能开发中...</div>
        </div>
    </div>

    <script src="releaseProcess.js"></script>
</body>
</html>
