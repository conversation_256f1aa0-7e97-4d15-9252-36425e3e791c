<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <title>分支检查布局测试</title>
    <style>
        body {
            margin: 20px;
            font-family: Arial, sans-serif;
            background-color: #f5f5f5;
        }

        /* 分支检查区域样式 */
        .branch-check-section h4 {
            margin: 0 0 15px 0;
            color: #333;
            font-size: 16px;
            font-weight: bold;
        }

        .branch-check-section h5 {
            margin: 0 0 10px 0;
            color: #333;
            font-size: 16px;
            font-weight: bold;
            border-bottom: 1px solid #eee;
            padding-bottom: 8px;
        }

        .branch-check-section .project-container {
            margin-bottom: 20px;
            border: 1px solid #eee;
            border-radius: 4px;
            padding: 15px;
            background: white;
        }

        /* 分支表格样式 */
        .branch-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 10px;
            table-layout: fixed;
        }

        .branch-table th,
        .branch-table td {
            padding: 8px 12px;
            text-align: left;
            border: 1px solid #ddd;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }

        .branch-table th {
            background-color: #f5f5f5;
            font-weight: bold;
        }

        /* 分支名称列 - 自适应宽度 */
        .branch-name-col {
            width: calc(100% - 120px); /* 总宽度减去状态列宽度 */
        }

        /* 状态列 - 固定100字符宽度 */
        .branch-status-col {
            width: 120px;
            min-width: 120px;
            max-width: 120px;
        }

        /* 状态样式 */
        .status-success {
            color: #4CAF50;
            font-weight: bold;
            display: inline-flex;
            align-items: center;
            gap: 4px;
        }

        .status-warning {
            color: #FF9800;
            font-weight: bold;
            display: inline-flex;
            align-items: center;
            gap: 4px;
        }

        .status-error {
            color: #f44336;
            font-weight: bold;
            display: inline-flex;
            align-items: center;
            gap: 4px;
        }

        /* 分支不存在警告样式 */
        .no-branches-warning {
            display: flex;
            align-items: flex-start;
            gap: 12px;
            padding: 16px;
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 4px;
            margin-top: 10px;
        }

        .warning-icon {
            font-size: 20px;
            flex-shrink: 0;
        }

        .warning-text {
            flex: 1;
        }

        .warning-text strong {
            color: #856404;
            font-size: 14px;
            display: block;
            margin-bottom: 4px;
        }

        .warning-text p {
            color: #856404;
            font-size: 13px;
            margin: 0;
            line-height: 1.4;
        }

        .test-info {
            background: #e3f2fd;
            padding: 15px;
            border-radius: 4px;
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <h1>分支检查布局测试</h1>
    
    <div class="test-info">
        <h3>布局要求：</h3>
        <ul>
            <li><strong>项目列表：</strong>根据项目分组显示</li>
            <li><strong>有分支：</strong>展示分支列表表格</li>
            <li><strong>无分支：</strong>显示警告提示"分支不存在，需检查"</li>
            <li><strong>状态列：</strong>固定宽度120px（约100字符）</li>
            <li><strong>分支名称列：</strong>自适应剩余宽度，超出显示省略号</li>
        </ul>
    </div>

    <div class="branch-check-section">
        <h4>分支检查结果</h4>
        <div style="margin-bottom: 20px;">
            <p><strong>Release名称:</strong> TIC生产发布-250812</p>
        </div>

        <!-- 有分支的项目示例 -->
        <div class="project-container">
            <h5>项目: sgsmall</h5>
            <table class="branch-table">
                <thead>
                    <tr>
                        <th class="branch-name-col">分支名称</th>
                        <th class="branch-status-col">状态</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td class="branch-name-col" title="TIC-32453-release-250812">TIC-32453-release-250812</td>
                        <td class="branch-status-col">
                            <span class="status-success">✓ 分支存在</span>
                        </td>
                    </tr>
                    <tr>
                        <td class="branch-name-col" title="这是一个非常长的分支名称用来测试超出宽度时的显示效果和省略号功能">这是一个非常长的分支名称用来测试超出宽度时的显示效果和省略号功能</td>
                        <td class="branch-status-col">
                            <span class="status-success">✓ 分支存在</span>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>

        <!-- 有多个分支的项目示例 -->
        <div class="project-container">
            <h5>项目: ticRoWeb</h5>
            <table class="branch-table">
                <thead>
                    <tr>
                        <th class="branch-name-col">分支名称</th>
                        <th class="branch-status-col">状态</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td class="branch-name-col">TIC-32591-release-250812</td>
                        <td class="branch-status-col">
                            <span class="status-success">✓ 分支存在</span>
                        </td>
                    </tr>
                    <tr>
                        <td class="branch-name-col">TIC-31690-release-250717</td>
                        <td class="branch-status-col">
                            <span class="status-success">✓ 分支存在</span>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>

        <!-- 有分支的项目示例 -->
        <div class="project-container">
            <h5>项目: ticOrder</h5>
            <table class="branch-table">
                <thead>
                    <tr>
                        <th class="branch-name-col">分支名称</th>
                        <th class="branch-status-col">状态</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td class="branch-name-col">TIC-32591_ro订单搜索优化</td>
                        <td class="branch-status-col">
                            <span class="status-success">✓ 分支存在</span>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>

        <!-- 无分支的项目示例 -->
        <div class="project-container">
            <h5>项目: ticOpenPlatform</h5>
            <div class="no-branches-warning">
                <div class="warning-icon">⚠️</div>
                <div class="warning-text">
                    <strong>分支不存在，需检查</strong>
                    <p>该项目下未找到对应的分支信息，请检查分支是否已创建。</p>
                </div>
            </div>
        </div>

        <!-- 另一个无分支的项目示例 -->
        <div class="project-container">
            <h5>项目: ticPayment</h5>
            <div class="no-branches-warning">
                <div class="warning-icon">⚠️</div>
                <div class="warning-text">
                    <strong>分支不存在，需检查</strong>
                    <p>该项目下未找到对应的分支信息，请检查分支是否已创建。</p>
                </div>
            </div>
        </div>
    </div>

    <div style="margin-top: 30px; padding: 15px; background: #f0f0f0; border-radius: 4px;">
        <h3>测试说明：</h3>
        <ul>
            <li>状态列固定宽度120px，确保能显示约100个字符</li>
            <li>分支名称列自适应剩余宽度</li>
            <li>超长分支名称会显示省略号，鼠标悬停显示完整内容</li>
            <li>无分支项目显示醒目的警告提示</li>
            <li>有分支项目以表格形式展示分支列表</li>
        </ul>
    </div>

</body>
</html>
