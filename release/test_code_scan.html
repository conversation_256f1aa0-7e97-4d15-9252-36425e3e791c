<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <title>代码扫描功能测试</title>
    <style>
        body {
            margin: 20px;
            font-family: Arial, sans-serif;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        .test-button {
            padding: 10px 20px;
            margin: 5px;
            background: #4CAF50;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        .test-button:hover {
            background: #45a049;
        }
        .result {
            margin-top: 10px;
            padding: 10px;
            background: #f5f5f5;
            border-radius: 4px;
            font-family: monospace;
            font-size: 12px;
            max-height: 400px;
            overflow-y: auto;
        }
        .success {
            color: #4CAF50;
        }
        .error {
            color: #f44336;
        }
        .info {
            color: #2196F3;
        }
    </style>
</head>
<body>
    <h1>代码扫描功能测试</h1>
    
    <div class="test-section">
        <h3>🔍 功能说明</h3>
        <p>代码扫描功能流程：</p>
        <ol>
            <li>使用缓存的branchMap keys（从getReleaseIssue接口获取）</li>
            <li>与app.json中的component字段匹配</li>
            <li>获取对应的sonarProject值</li>
            <li>调用querySonarResult接口获取扫描结果</li>
            <li>按项目显示扫描结果列表</li>
        </ol>
    </div>

    <div class="test-section">
        <h3>📋 测试步骤</h3>
        <div>
            <label>模拟branchMap keys: </label>
            <input type="text" id="mock-keys" value="TIC Mall View,TIC Mall,TIC Leads" style="padding: 8px; width: 300px;">
            <p style="font-size: 12px; color: #666;">多个组件用逗号分隔</p>
        </div>
        
        <button class="test-button" onclick="testAppConfigMatch()">测试app.json匹配</button>
        <button class="test-button" onclick="testSonarAPI()">测试Sonar接口</button>
        <button class="test-button" onclick="testCompleteFlow()">测试完整流程</button>
        
        <div id="test-result" class="result"></div>
    </div>

    <div class="test-section">
        <h3>🚀 实际功能测试</h3>
        <button class="test-button" onclick="openRealPage()">打开Release发布流程页面</button>
        <div style="background: #e3f2fd; padding: 10px; border-radius: 4px; margin-top: 10px;">
            <strong>测试步骤：</strong>
            <ol>
                <li>打开页面，选择一个版本并查询</li>
                <li>切换到"代码扫描"Tab</li>
                <li>观察是否正确显示扫描结果</li>
                <li>检查控制台日志了解详细过程</li>
            </ol>
        </div>
    </div>

    <script>
        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const className = type === 'error' ? 'error' : (type === 'success' ? 'success' : 'info');
            return `<div class="${className}">[${timestamp}] ${message}</div>`;
        }

        // 测试app.json匹配
        async function testAppConfigMatch() {
            const resultDiv = document.getElementById('test-result');
            const mockKeys = document.getElementById('mock-keys').value.split(',').map(k => k.trim());
            
            resultDiv.innerHTML = log('开始测试app.json匹配...');
            resultDiv.innerHTML += log(`模拟branchMap keys: [${mockKeys.join(', ')}]`);
            
            try {
                // 加载app.json
                const response = await fetch('../json/app.json');
                const appConfig = await response.json();
                
                resultDiv.innerHTML += log(`✓ 成功加载app.json，共${appConfig.length}个配置项`, 'success');
                
                // 匹配组件
                const matchedProjects = [];
                
                mockKeys.forEach(componentName => {
                    const matchedApp = appConfig.find(app => app.component === componentName);
                    
                    if (matchedApp) {
                        const projectKey = matchedApp.sonarProject || matchedApp.gitName || componentName;
                        
                        matchedProjects.push({
                            component: componentName,
                            sonarProject: projectKey,
                            appHierarchy: matchedApp.appHierarchy,
                            gitName: matchedApp.gitName
                        });
                        
                        resultDiv.innerHTML += log(`✓ 匹配: ${componentName} -> ${projectKey} (${matchedApp.appHierarchy})`, 'success');
                    } else {
                        resultDiv.innerHTML += log(`❌ 未匹配: ${componentName}`, 'error');
                    }
                });
                
                resultDiv.innerHTML += log(`匹配结果: ${JSON.stringify(matchedProjects, null, 2)}`);
                
            } catch (error) {
                resultDiv.innerHTML += log(`❌ 测试失败: ${error.message}`, 'error');
            }
        }

        // 测试Sonar接口
        async function testSonarAPI() {
            const resultDiv = document.getElementById('test-result');
            
            resultDiv.innerHTML = log('开始测试Sonar接口...');
            
            // 模拟项目列表
            const mockProjects = ['ticLeads', 'ticMall', 'sgsmall'];
            
            try {
                resultDiv.innerHTML += log(`调用接口参数: ${JSON.stringify(mockProjects)}`);
                
                const response = await fetch('http://10.169.128.35:8611/business/api/v1/release/release/querySonarResult', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(mockProjects)
                });

                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }

                const data = await response.json();
                
                resultDiv.innerHTML += log(`✓ 接口调用成功`, 'success');
                resultDiv.innerHTML += log(`返回结果: ${JSON.stringify(data, null, 2)}`);
                
                if (data.resultCode === "0") {
                    resultDiv.innerHTML += log(`✓ 获取到${data.data.length}个项目的扫描结果`, 'success');
                } else {
                    resultDiv.innerHTML += log(`❌ 接口返回错误: ${data.resultMsg}`, 'error');
                }
                
            } catch (error) {
                resultDiv.innerHTML += log(`❌ 接口调用失败: ${error.message}`, 'error');
            }
        }

        // 测试完整流程
        async function testCompleteFlow() {
            const resultDiv = document.getElementById('test-result');
            const mockKeys = document.getElementById('mock-keys').value.split(',').map(k => k.trim());
            
            resultDiv.innerHTML = log('开始测试完整流程...');
            
            try {
                // 步骤1: 加载app.json
                resultDiv.innerHTML += log('步骤1: 加载app.json配置');
                const appResponse = await fetch('../json/app.json');
                const appConfig = await appResponse.json();
                resultDiv.innerHTML += log(`✓ 加载完成，共${appConfig.length}个配置`, 'success');
                
                // 步骤2: 匹配组件
                resultDiv.innerHTML += log('步骤2: 匹配组件与Sonar项目');
                const matchedProjects = [];
                
                mockKeys.forEach(componentName => {
                    const matchedApp = appConfig.find(app => app.component === componentName);
                    
                    if (matchedApp) {
                        const projectKey = matchedApp.sonarProject || matchedApp.gitName || componentName;
                        matchedProjects.push({
                            component: componentName,
                            sonarProject: projectKey,
                            appHierarchy: matchedApp.appHierarchy,
                            gitName: matchedApp.gitName
                        });
                    }
                });
                
                resultDiv.innerHTML += log(`✓ 匹配到${matchedProjects.length}个项目`, 'success');
                
                // 步骤3: 调用Sonar接口
                if (matchedProjects.length > 0) {
                    resultDiv.innerHTML += log('步骤3: 调用Sonar接口');
                    const sonarProjects = matchedProjects.map(p => p.sonarProject);
                    
                    const sonarResponse = await fetch('http://10.169.128.35:8611/business/api/v1/release/release/querySonarResult', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify(sonarProjects)
                    });

                    const sonarData = await sonarResponse.json();
                    
                    if (sonarData.resultCode === "0") {
                        resultDiv.innerHTML += log(`✓ 获取到${sonarData.data.length}个项目的扫描结果`, 'success');
                        resultDiv.innerHTML += log('扫描结果预览:');
                        
                        sonarData.data.forEach(result => {
                            const projectInfo = matchedProjects.find(p => p.sonarProject === result.projectKey);
                            const componentName = projectInfo ? projectInfo.component : result.projectKey;
                            
                            resultDiv.innerHTML += log(`  ${componentName}: ${result.alertStatus}, Bugs: ${result.bugs}, 漏洞: ${result.vulnerabilities}`);
                        });
                        
                        resultDiv.innerHTML += log('✅ 完整流程测试成功！', 'success');
                    } else {
                        resultDiv.innerHTML += log(`❌ Sonar接口返回错误: ${sonarData.resultMsg}`, 'error');
                    }
                } else {
                    resultDiv.innerHTML += log('❌ 没有匹配的项目，无法调用Sonar接口', 'error');
                }
                
            } catch (error) {
                resultDiv.innerHTML += log(`❌ 完整流程测试失败: ${error.message}`, 'error');
            }
        }

        // 打开实际页面
        function openRealPage() {
            window.open('./releaseProcess.html', '_blank');
        }
    </script>
</body>
</html>
