
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <title>Release查询</title>
    <style>
        body {
            margin: 0;
            padding: 15px;
            font-family: Arial, sans-serif;
            font-size: 14px;
        }

        h2 {
            margin: 0 0 20px 0;
            font-size: 16px;
            color: #333;
        }

        .form-container {
            display: flex;
            align-items: center;
            gap: 15px;
            margin-bottom: 20px;
        }

        .form-group {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        label {
            white-space: nowrap;
            font-weight: bold;
            color: #666;
        }

        .select-container {
            position: relative;
            width: 200px;
        }

        .search-input {
            width: 100%;
            padding: 8px;
            box-sizing: border-box;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }

        .search-input:focus {
            border-color: #1e88e5;
            outline: none;
        }

        button {
            padding: 8px 20px;
            background-color: #4CAF50;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            white-space: nowrap;
        }

        button:hover {
            background-color: #45a049;
        }

        .select-options {
            position: absolute;
            top: 100%;
            left: 0;
            right: 0;
            max-height: 200px;
            overflow-y: auto;
            background: white;
            border: 1px solid #ddd;
            border-radius: 4px;
            margin-top: 4px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            z-index: 1000;
            display: none;
        }

        .select-option {
            padding: 8px;
            cursor: pointer;
        }

        .select-option:hover {
            background-color: #f5f5f5;
        }

        #result {
            margin-top: 20px;
        }

        .error {
            color: #f44336;
            padding: 15px;
            background-color: #ffebee;
            border-radius: 4px;
            margin: 10px 0;
        }

        .loading {
            text-align: center;
            padding: 20px;
            color: #666;
        }

        .release-section {
            margin-bottom: 30px;
            border: 1px solid #ddd;
            border-radius: 6px;
            overflow: hidden;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        
        .section-title {
            margin: 0;
            padding: 15px;
            background-color: #f5f5f5;
            border-bottom: 1px solid #ddd;
            font-size: 18px;
            color: #333;
        }
        
        .release-info {
            display: flex;
            padding: 15px;
            gap: 20px;
        }
        
        .issues-container {
            flex: 1;
            min-width: 0; /* 防止flex子项溢出 */
        }
        
        .git-repos-section {
            width: 300px;
            background: #f9f9f9;
            border-radius: 4px;
            padding: 15px;
            border: 1px solid #eee;
        }
        
        .git-repos-section h3 {
            margin: 0 0 15px 0;
            color: #333;
            font-size: 16px;
            padding-bottom: 10px;
            border-bottom: 1px solid #ddd;
        }
        
        .hierarchy-group {
            margin-bottom: 15px;
        }
        
        .hierarchy-group h4 {
            margin: 0 0 10px 0;
            color: #555;
            font-size: 14px;
            display: flex;
            align-items: center;
        }
        
        .hierarchy-group h4::before {
            content: '►';
            margin-right: 5px;
            font-size: 12px;
            color: #4CAF50;
        }
        
        .hierarchy-group ul {
            margin: 0;
            padding-left: 20px;
            list-style-type: none;
        }
        
        .hierarchy-group li {
            margin: 5px 0;
            color: #444;
            padding: 5px 10px;
            background: #fff;
            border-radius: 4px;
            font-size: 13px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        }
        
        /* Checklist样式 */
        .checklist-container {
            padding: 15px;
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
            gap: 15px;
        }
        
        .checklist-item {
            display: flex;
            align-items: center;
            padding: 12px 15px;
            background: #f9f9f9;
            border-radius: 4px;
            border: 1px solid #eee;
            transition: background-color 0.2s;
        }
        
        .checklist-item:hover {
            background-color: #f0f0f0;
        }
        
        .checklist-item input[type="radio"] {
            margin-right: 10px;
            width: 18px;
            height: 18px;
        }
        
        .checklist-item label {
            font-size: 14px;
            color: #333;
            cursor: pointer;
        }
        
        /* 调整表格样式 */
        table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 10px;
            background-color: white;
            table-layout: fixed;
        }
        
        th, td {
            padding: 12px 8px;
            text-align: left;
            border: 1px solid #ddd;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }
        
        th:first-child, td:first-child {
            width: 50%;
        }
        
        th:nth-child(2), td:nth-child(2) {
            width: 20%;
        }
        
        th:last-child, td:last-child {
            width: 30%;
        }
        
        /* 修复section-header样式，使标题和按钮在同一行 */
        .section-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 10px 15px;
            background-color: #f9f9f9;
            border: 1px solid #eee;
            border-radius: 4px;
            margin-bottom: 10px;
        }
        
        .section-header h4 {
            margin: 0;
            font-size: 16px;
            color: #333;
        }
        
        .toggle-btn {
            background: #fff;
            border: 1px solid #ddd;
            border-radius: 50%;
            width: 24px;
            height: 24px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            font-size: 16px;
            line-height: 1;
            padding: 0;
            color: #666;
        }
        
        .toggle-btn:hover {
            background-color: #f0f0f0;
            color: #333;
        }
        
        /* 添加checklist标题区域样式 */
        .checklist-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 15px;
            border-bottom: 1px solid #eee;
            margin-bottom: 15px; /* 增加底部间距 */
        }
        
        .generate-btn {
            padding: 8px 16px;
            background-color: #4CAF50;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            transition: background-color 0.2s;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .generate-btn:hover {
            background-color: #45a049;
            box-shadow: 0 3px 6px rgba(0,0,0,0.15);
        }
        
        /* 添加checklist选项组样式 */
        .checklist-options {
            padding: 0 15px 15px;
            display: flex;
            flex-wrap: wrap;
            gap: 30px;
        }
        
        .checklist-option-group {
            flex: 1;
            min-width: 250px;
        }
        
        .checklist-option-group h4 {
            margin: 0 0 15px 0;
            padding-bottom: 8px;
            border-bottom: 1px solid #eee;
            color: #333;
            font-size: 15px;
        }
        
        .option-items {
            display: flex;
            flex-direction: column;
            gap: 10px;
        }
        
        /* 优化radio按钮样式 */
        .checklist-item {
            display: flex;
            align-items: center;
            padding: 12px 15px;
            background: #f9f9f9;
            border-radius: 4px;
            border: 1px solid #eee;
            transition: background-color 0.2s;
        }
        
        .checklist-item:hover {
            background-color: #f0f0f0;
        }
        
        .checklist-item input[type="radio"] {
            margin-right: 10px;
            width: 18px;
            height: 18px;
        }
        
        .checklist-item label {
            font-size: 14px;
            color: #333;
            cursor: pointer;
        }
        
        /* 发布清单样式 */
        .deployment-section {
            margin-bottom: 20px;
            border: 1px solid #eee;
            border-radius: 5px;
            overflow: hidden;
        }
        
        .section-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px 15px;
            background-color: #f5f5f5;
            border-bottom: 1px solid #eee;
        }
        
        .section-header h4 {
            margin: 0;
            font-size: 16px;
            font-weight: bold;
            color: #333;
        }
        
        .section-content {
            padding: 15px;
        }
        
        .owner-section {
            margin-bottom: 20px;
        }
        
        .owner-name {
            margin: 0 0 10px 0;
            font-size: 14px;
            color: #555;
            font-weight: bold;
        }
        
        .release-list-content table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 15px;
            table-layout: fixed;
        }
        
        .release-list-content th,
        .release-list-content td {
            padding: 8px 10px;
            text-align: left;
            border: 1px solid #eee;
            vertical-align: top;
        }
        
        .release-list-content th {
            background-color: #f9f9f9;
            font-weight: 600;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }
        
        .release-list-content td {
            word-break: break-word;
        }
        
        .release-list-content th:nth-child(1),
        .release-list-content td:nth-child(1) {
            width: 15%;
        }
        
        .release-list-content th:nth-child(2),
        .release-list-content td:nth-child(2) {
            width: 8%;
        }
        
        .release-list-content th:nth-child(3),
        .release-list-content td:nth-child(3) {
            width: 8%;
        }
        
        .release-list-content th:nth-child(4),
        .release-list-content td:nth-child(4) {
            width: 15%;
        }
        
        .release-list-content th:nth-child(5),
        .release-list-content td:nth-child(5) {
            width: 20%;
        }
        
        .release-list-content th:nth-child(6),
        .release-list-content td:nth-child(6) {
            width: 24%;
        }
        
        .release-list-content th:nth-child(7),
        .release-list-content td:nth-child(7) {
            width: 10%;
        }
        
        /* 状态标签样式 */
        .status-badge {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: 500;
        }
        
        .status-pending {
            background-color: #FFF3E0;
            color: #E65100;
        }
        
        /* 环境选择区域样式 */
        .environment-selection {
            margin-top: 15px;
            padding: 15px;
            background: #f5f5f5;
            border-radius: 6px;
        }
        
        .environment-selection h3 {
            margin: 0 0 10px 0;
            color: #333;
            font-size: 16px;
        }

        /* Tab样式 */
        .tabs-container {
            width: 100%;
        }
        
        .tabs {
            display: flex;
            border-bottom: 1px solid #ddd;
            margin-bottom: 15px;
        }
        
        .tab {
            padding: 10px 20px;
            cursor: pointer;
            border: 1px solid transparent;
            border-bottom: none;
            border-radius: 4px 4px 0 0;
            margin-right: 5px;
            background-color: #f5f5f5;
            transition: all 0.3s;
        }
        
        .tab:hover {
            background-color: #e9e9e9;
        }
        
        .tab.active {
            background-color: #fff;
            border-color: #ddd;
            border-bottom-color: #fff;
            margin-bottom: -1px;
            font-weight: bold;
            color: #4CAF50;
        }
        
        .tab-content {
            display: none;
            padding: 15px;
            border: 1px solid #ddd;
            border-top: none;
            border-radius: 0 0 4px 4px;
        }
        
        .tab-content.active {
            display: block;
        }

        /* 环境选项横向排列样式 */
        .option-items.horizontal {
            display: flex;
            flex-direction: row;
            gap: 15px;
        }
        
        .option-items.horizontal .checklist-item {
            flex: 0 0 auto;
            min-width: 120px;
        }
        
        /* 调整Issue列宽度设置 */
        .issues-container table {
            table-layout: fixed;
            width: 100%;
        }
        
        .issues-container th:nth-child(1),
        .issues-container td:nth-child(1) {
            width: 120px; /* Issue列宽度，支持16位字母 */
            min-width: 120px;
        }
        
        .issues-container th:nth-child(2),
        .issues-container td:nth-child(2) {
            width: auto; /* Title列自适应剩余空间 */
            min-width: 200px; /* 确保有足够空间显示内容 */
        }
        
        .issues-container th:nth-child(3),
        .issues-container td:nth-child(3) {
            width: 120px; /* Assignee列宽度 */
        }
        
        .issues-container th:nth-child(4),
        .issues-container td:nth-child(4) {
            width: 300px; /* Components列宽度 */
        }
        
        .issues-container th:nth-child(5),
        .issues-container td:nth-child(5) {
            width: 100px; /* Status列宽度 */
        }
        
        /* 确保单元格内容溢出时显示省略号 */
        .issues-container td {
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            padding: 8px;
        }
        
        /* Title列允许换行 */
        .issues-container td:nth-child(2) {
            white-space: normal;
            word-break: break-word;
            max-width: 100%;
            line-height: 1.4;
        }
        
        /* 确保链接在Title列中正确显示 */
        .issues-container td:nth-child(2) a {
            word-break: break-word;
            display: inline-block;
            max-width: 100%;
        }

        /* 历史版本样式 */
        .history-versions-container {
            padding: 15px;
        }

        .history-versions-list {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 10px;
            margin-top: 10px;
        }

        .history-version-item {
            display: flex;
            align-items: center;
            padding: 12px 15px;
            background: #f9f9f9;
            border: 1px solid #eee;
            border-radius: 4px;
            transition: background-color 0.2s;
        }

        .history-version-item:hover {
            background-color: #f0f0f0;
        }

        .version-index {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            width: 24px;
            height: 24px;
            background: #4CAF50;
            color: white;
            border-radius: 50%;
            font-size: 12px;
            font-weight: bold;
            margin-right: 12px;
            flex-shrink: 0;
        }

        .version-name {
            flex: 1;
            font-size: 14px;
            color: #333;
            word-break: break-word;
        }

        .info {
            text-align: center;
            padding: 20px;
            color: #666;
            font-style: italic;
        }

        /* 自适应加载和内容切换样式 */
        .loading {
            text-align: center;
            padding: 20px;
            color: #666;
            background: #f9f9f9;
            border-radius: 4px;
            margin: 10px 0;
            transition: opacity 0.3s ease;
        }

        .loading::after {
            content: '';
            display: inline-block;
            width: 16px;
            height: 16px;
            margin-left: 8px;
            border: 2px solid #ddd;
            border-top: 2px solid #4CAF50;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* 内容区域自适应样式 */
        .issues-container,
        .git-repos-section,
        .history-versions-container {
            min-height: 60px;
            transition: all 0.3s ease;
        }

        /* 确保内容切换时的平滑过渡 */
        #issues-content,
        #git-repos-content,
        #history-versions-content {
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        #issues-content[style*="block"],
        #git-repos-content[style*="block"],
        #history-versions-content[style*="block"] {
            opacity: 1;
        }

        /* 响应式布局优化 */
        @media (max-width: 768px) {
            .release-info {
                flex-direction: column;
            }

            .git-repos-section {
                width: 100%;
                margin-top: 20px;
            }

            .history-versions-list {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <h2>Release查询</h2>
    <form id="releaseForm" class="form-container">
        <div class="form-group">
            <label for="versionSearch">Release版本号:</label>
            <div class="select-container">
                <input type="text" id="versionSearch" class="search-input" placeholder="搜索版本号...">
                <div id="versionOptions" class="select-options"></div>
            </div>
        </div>
        <button type="submit">查询</button>
    </form>
    <div id="result"></div>
    <script src="release.js"></script>
</body>
</html>
