<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <title>开发模块功能测试</title>
    <style>
        body {
            margin: 20px;
            font-family: Arial, sans-serif;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        .test-button {
            padding: 10px 20px;
            margin: 5px;
            background: #4CAF50;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        .test-button:hover {
            background: #45a049;
        }
        .result {
            margin-top: 10px;
            padding: 10px;
            background: #f5f5f5;
            border-radius: 4px;
            max-height: 400px;
            overflow-y: auto;
        }
        .mock-data {
            background: #e8f5e8;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <h1>开发模块功能测试</h1>
    
    <div class="test-section">
        <h3>1. 测试分支检查API</h3>
        <input type="text" id="test-version" placeholder="输入版本号，如：TIC生产发布-250812" style="padding: 8px; margin-right: 10px; width: 300px;">
        <button class="test-button" onclick="testCheckBranch()">测试分支检查</button>
        <div id="branch-result" class="result"></div>
    </div>

    <div class="test-section">
        <h3>2. 模拟数据测试布局</h3>
        <button class="test-button" onclick="testMockData()">测试模拟数据布局</button>
        <div class="mock-data">
            <strong>模拟返回数据结构：</strong>
            <pre>{
  "resultCode": "0",
  "resultMsg": "成功",
  "data": {
    "projectMap": {
      "ticOpenPlatform": [],
      "sgsmall": ["TIC-32453-release-250812"],
      "ticOrder": ["TIC-32591_ro订单搜索优化"],
      "ticRoWeb": ["TIC-32591-release-250812", "TIC-31690-release-250717"]
    }
  }
}</pre>
        </div>
        <div id="mock-result" class="result"></div>
    </div>

    <div class="test-section">
        <h3>3. 打开完整开发模块</h3>
        <button class="test-button" onclick="openDevelopmentModule()">打开Release发布流程页面</button>
    </div>

    <script>
        // 测试分支检查API
        async function testCheckBranch() {
            const version = document.getElementById('test-version').value;
            const resultDiv = document.getElementById('branch-result');
            
            if (!version) {
                resultDiv.innerHTML = '<div style="color: red;">请输入版本号</div>';
                return;
            }
            
            resultDiv.innerHTML = '查询中...';
            
            try {
                const response = await fetch('http://*************:8611/business/api/v1/release/release/checkBranch', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        releaseName: version
                    })
                });

                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }

                const data = await response.json();
                
                if (data.resultCode === "0" && data.data && data.data.projectMap) {
                    resultDiv.innerHTML = generateBranchCheckHTML(data.data.projectMap, version);
                } else {
                    resultDiv.innerHTML = `<div style="color: orange;">API返回异常: ${JSON.stringify(data, null, 2)}</div>`;
                }
            } catch (error) {
                resultDiv.innerHTML = `<div style="color: red;">错误: ${error.message}</div>`;
            }
        }

        // 测试模拟数据
        function testMockData() {
            const mockData = {
                "ticOpenPlatform": [],
                "sgsmall": ["TIC-32453-release-250812"],
                "ticOrder": ["TIC-32591_ro订单搜索优化"],
                "ticRoWeb": ["TIC-32591-release-250812", "TIC-31690-release-250717"]
            };
            
            const resultDiv = document.getElementById('mock-result');
            resultDiv.innerHTML = generateBranchCheckHTML(mockData, "TIC生产发布-250812");
        }

        // 生成分支检查HTML（复制自releaseProcess.js的逻辑）
        function generateBranchCheckHTML(projectMap, releaseName) {
            let html = `
                <div style="border: 1px solid #eee; border-radius: 4px; padding: 15px; background: white;">
                    <h4 style="margin: 0 0 15px 0; color: #333;">分支检查结果</h4>
                    <div style="margin-bottom: 20px;">
                        <p><strong>Release名称:</strong> ${releaseName}</p>
                    </div>
            `;

            // 遍历项目映射
            Object.entries(projectMap).forEach(([projectName, branches]) => {
                html += `
                    <div style="margin-bottom: 20px; border: 1px solid #eee; border-radius: 4px; padding: 15px; background: #fafafa;">
                        <h5 style="margin: 0 0 10px 0; color: #333; font-size: 16px; border-bottom: 1px solid #eee; padding-bottom: 8px;">项目: ${projectName}</h5>
                `;

                if (branches && branches.length > 0) {
                    // 有分支列表，展示分支列表
                    html += `
                        <table style="width: 100%; border-collapse: collapse; margin-top: 10px; table-layout: fixed;">
                            <thead>
                                <tr>
                                    <th style="padding: 8px 12px; text-align: left; border: 1px solid #ddd; background-color: #f5f5f5; font-weight: bold; width: calc(100% - 120px);">分支名称</th>
                                    <th style="padding: 8px 12px; text-align: left; border: 1px solid #ddd; background-color: #f5f5f5; font-weight: bold; width: 120px; min-width: 120px; max-width: 120px;">状态</th>
                                </tr>
                            </thead>
                            <tbody>
                    `;

                    branches.forEach(branch => {
                        const branchTitle = branch.length > 50 ? branch : '';
                        html += `
                            <tr>
                                <td style="padding: 8px 12px; text-align: left; border: 1px solid #ddd; overflow: hidden; text-overflow: ellipsis; white-space: nowrap;" ${branchTitle ? `title="${branchTitle}"` : ''}>${branch}</td>
                                <td style="padding: 8px 12px; text-align: left; border: 1px solid #ddd; width: 120px; min-width: 120px; max-width: 120px;">
                                    <span style="color: #4CAF50; font-weight: bold; display: inline-flex; align-items: center; gap: 4px;">✓ 分支存在</span>
                                </td>
                            </tr>
                        `;
                    });

                    html += `
                            </tbody>
                        </table>
                    `;
                } else {
                    // 分支为空，提示分支不存在
                    html += `
                        <div style="display: flex; align-items: flex-start; gap: 12px; padding: 16px; background-color: #fff3cd; border: 1px solid #ffeaa7; border-radius: 4px; margin-top: 10px;">
                            <div style="font-size: 20px; flex-shrink: 0;">⚠️</div>
                            <div style="flex: 1;">
                                <strong style="color: #856404; font-size: 14px; display: block; margin-bottom: 4px;">分支不存在，需检查</strong>
                                <p style="color: #856404; font-size: 13px; margin: 0; line-height: 1.4;">该项目下未找到对应的分支信息，请检查分支是否已创建。</p>
                            </div>
                        </div>
                    `;
                }

                html += `</div>`;
            });

            html += `</div>`;
            return html;
        }

        // 打开完整页面
        function openDevelopmentModule() {
            window.open('./releaseProcess.html', '_blank');
        }
    </script>
</body>
</html>
