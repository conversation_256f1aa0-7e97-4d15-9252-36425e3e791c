<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <title>元素检查测试</title>
    <style>
        body {
            margin: 20px;
            font-family: Arial, sans-serif;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        .test-button {
            padding: 10px 20px;
            margin: 5px;
            background: #4CAF50;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        .test-button:hover {
            background: #45a049;
        }
        .result {
            margin-top: 10px;
            padding: 10px;
            background: #f5f5f5;
            border-radius: 4px;
            font-family: monospace;
            font-size: 12px;
        }
        .success {
            color: #4CAF50;
        }
        .error {
            color: #f44336;
        }
        .warning {
            color: #FF9800;
        }
    </style>
</head>
<body>
    <h1>页面元素检查测试</h1>
    
    <div class="test-section">
        <h3>🔍 问题分析</h3>
        <p>错误信息显示：<code>Cannot read properties of null (reading 'style')</code></p>
        <p>这说明 <code>document.getElementById('requirements-loading')</code> 返回了 <code>null</code></p>
    </div>

    <div class="test-section">
        <h3>📋 元素存在性检查</h3>
        <button class="test-button" onclick="checkElements()">检查页面元素</button>
        <div id="element-result" class="result"></div>
    </div>

    <div class="test-section">
        <h3>🎯 Tab状态检查</h3>
        <button class="test-button" onclick="checkTabStates()">检查Tab状态</button>
        <div id="tab-result" class="result"></div>
    </div>

    <div class="test-section">
        <h3>🚀 打开实际页面进行检查</h3>
        <button class="test-button" onclick="openPageWithCheck()">打开页面并检查元素</button>
        <div style="background: #e3f2fd; padding: 10px; border-radius: 4px; margin-top: 10px;">
            <strong>检查步骤：</strong>
            <ol>
                <li>点击上面的按钮打开页面</li>
                <li>页面会自动在控制台输出元素检查结果</li>
                <li>然后尝试选择版本并查询</li>
                <li>观察是否还有元素找不到的错误</li>
            </ol>
        </div>
    </div>

    <script>
        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const className = type === 'error' ? 'error' : (type === 'success' ? 'success' : (type === 'warning' ? 'warning' : ''));
            return `<div class="${className}">[${timestamp}] ${message}</div>`;
        }

        // 检查页面元素
        function checkElements() {
            const resultDiv = document.getElementById('element-result');
            resultDiv.innerHTML = log('开始检查页面元素...');
            
            // 需要检查的元素列表
            const elementsToCheck = [
                'requirements-loading',
                'requirements-content',
                'development-loading',
                'development-content',
                'code-scan-content',
                'test-deploy-content',
                'uat-deploy-content',
                'prod-deploy-content',
                'release-check-content',
                'releaseForm',
                'versionSearch',
                'versionOptions',
                'history-versions-loading',
                'history-versions-content'
            ];
            
            let foundCount = 0;
            let missingCount = 0;
            
            elementsToCheck.forEach(elementId => {
                const element = document.getElementById(elementId);
                if (element) {
                    resultDiv.innerHTML += log(`✓ 找到元素: ${elementId}`, 'success');
                    foundCount++;
                } else {
                    resultDiv.innerHTML += log(`❌ 缺少元素: ${elementId}`, 'error');
                    missingCount++;
                }
            });
            
            resultDiv.innerHTML += log(`总结: 找到 ${foundCount} 个元素，缺少 ${missingCount} 个元素`);
            
            if (missingCount > 0) {
                resultDiv.innerHTML += log(`⚠️ 这解释了为什么会出现 null 错误`, 'warning');
            }
        }

        // 检查Tab状态
        function checkTabStates() {
            const resultDiv = document.getElementById('tab-result');
            resultDiv.innerHTML = log('开始检查Tab状态...');
            
            // 检查所有tab按钮
            const tabs = document.querySelectorAll('.tab');
            resultDiv.innerHTML += log(`找到 ${tabs.length} 个Tab按钮`);
            
            tabs.forEach((tab, index) => {
                const tabId = tab.getAttribute('data-tab');
                const isActive = tab.classList.contains('active');
                resultDiv.innerHTML += log(`Tab ${index + 1}: ${tabId} (${isActive ? '激活' : '未激活'})`);
            });
            
            // 检查所有tab内容
            const tabContents = document.querySelectorAll('.tab-content');
            resultDiv.innerHTML += log(`找到 ${tabContents.length} 个Tab内容`);
            
            tabContents.forEach((content, index) => {
                const contentId = content.id;
                const isActive = content.classList.contains('active');
                const isVisible = content.style.display !== 'none';
                resultDiv.innerHTML += log(`内容 ${index + 1}: ${contentId} (${isActive ? '激活' : '未激活'}, ${isVisible ? '可见' : '隐藏'})`);
            });
            
            // 检查当前激活的tab
            const activeTab = document.querySelector('.tab.active');
            if (activeTab) {
                const activeTabId = activeTab.getAttribute('data-tab');
                resultDiv.innerHTML += log(`当前激活Tab: ${activeTabId}`, 'success');
                
                const activeContent = document.getElementById(`${activeTabId}-content`);
                if (activeContent) {
                    resultDiv.innerHTML += log(`对应内容存在: ${activeTabId}-content`, 'success');
                } else {
                    resultDiv.innerHTML += log(`对应内容缺失: ${activeTabId}-content`, 'error');
                }
            } else {
                resultDiv.innerHTML += log(`没有激活的Tab`, 'warning');
            }
        }

        // 打开页面并进行检查
        function openPageWithCheck() {
            // 打开页面
            const newWindow = window.open('./releaseProcess.html', '_blank');
            
            // 等待页面加载完成后进行检查
            newWindow.addEventListener('load', function() {
                // 在新窗口中执行检查
                newWindow.console.log('🔍 开始自动元素检查...');
                
                const elementsToCheck = [
                    'requirements-loading',
                    'requirements-content',
                    'development-loading',
                    'development-content',
                    'releaseForm',
                    'versionSearch'
                ];
                
                let allElementsFound = true;
                
                elementsToCheck.forEach(elementId => {
                    const element = newWindow.document.getElementById(elementId);
                    if (element) {
                        newWindow.console.log(`✓ 找到元素: ${elementId}`, element);
                    } else {
                        newWindow.console.error(`❌ 缺少元素: ${elementId}`);
                        allElementsFound = false;
                    }
                });
                
                if (allElementsFound) {
                    newWindow.console.log('✅ 所有关键元素都存在，可以正常使用');
                } else {
                    newWindow.console.error('❌ 有元素缺失，可能会导致功能异常');
                }
                
                // 检查Tab状态
                const activeTab = newWindow.document.querySelector('.tab.active');
                if (activeTab) {
                    const tabId = activeTab.getAttribute('data-tab');
                    newWindow.console.log(`当前激活Tab: ${tabId}`);
                    
                    const tabContent = newWindow.document.getElementById(`${tabId}-content`);
                    if (tabContent) {
                        newWindow.console.log(`Tab内容存在: ${tabId}-content`);
                    } else {
                        newWindow.console.error(`Tab内容缺失: ${tabId}-content`);
                    }
                }
                
                newWindow.console.log('🎯 现在可以尝试选择版本并查询，观察是否还有错误');
            });
        }
    </script>
</body>
</html>
