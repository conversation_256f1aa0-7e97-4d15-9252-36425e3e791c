<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <title>Bug修复测试</title>
    <style>
        body {
            margin: 20px;
            font-family: Arial, sans-serif;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        .test-button {
            padding: 10px 20px;
            margin: 5px;
            background: #4CAF50;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        .test-button:hover {
            background: #45a049;
        }
        .result {
            margin-top: 10px;
            padding: 10px;
            background: #f5f5f5;
            border-radius: 4px;
            max-height: 300px;
            overflow-y: auto;
        }
        .success {
            color: #4CAF50;
            font-weight: bold;
        }
        .error {
            color: #f44336;
            font-weight: bold;
        }
        .info {
            color: #2196F3;
            font-weight: bold;
        }
        .mock-data {
            background: #e8f5e8;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <h1>Bug修复功能测试</h1>
    
    <div class="test-section">
        <h3>测试1: Tab切换重新调用请求</h3>
        <p>测试每次切换Tab时是否都会重新调用API请求</p>
        <button class="test-button" onclick="testTabSwitching()">模拟Tab切换测试</button>
        <div id="tab-result" class="result"></div>
    </div>

    <div class="test-section">
        <h3>测试2: 版本切换后重新查询</h3>
        <p>测试切换版本后点击查询是否会重新调用getReleaseIssue接口</p>
        <input type="text" id="version1" placeholder="版本1" value="TIC生产发布-250812" style="padding: 8px; margin: 5px;">
        <input type="text" id="version2" placeholder="版本2" value="TIC生产发布-250813" style="padding: 8px; margin: 5px;">
        <br>
        <button class="test-button" onclick="testVersionSwitching()">测试版本切换</button>
        <div id="version-result" class="result"></div>
    </div>

    <div class="test-section">
        <h3>测试3: branchMap Keys缓存</h3>
        <p>测试调用getReleaseIssue接口后是否正确缓存branchMap的keys</p>
        <input type="text" id="test-version-cache" placeholder="输入版本号" value="TIC生产发布-250812" style="padding: 8px; margin: 5px;">
        <button class="test-button" onclick="testBranchMapCache()">测试branchMap缓存</button>
        <div class="mock-data">
            <strong>期望的branchMap结构示例：</strong>
            <pre>{
  "branchMap": {
    "TIC Mall View": ["TIC-32453-release-250812"],
    "TIC Mall": ["TIC-32762-智能客服优化功能"]
  }
}</pre>
            <strong>期望缓存结果：</strong> ["TIC Mall View", "TIC Mall"]
        </div>
        <div id="cache-result" class="result"></div>
    </div>

    <div class="test-section">
        <h3>测试4: 完整流程测试</h3>
        <p>测试完整的查询 → 切换Tab → 切换版本 → 再次查询的流程</p>
        <button class="test-button" onclick="testCompleteFlow()">测试完整流程</button>
        <div id="flow-result" class="result"></div>
    </div>

    <div class="test-section">
        <h3>打开实际页面测试</h3>
        <button class="test-button" onclick="openRealPage()">打开Release发布流程页面</button>
    </div>

    <script>
        let testCallCount = 0;
        let mockCurrentReleaseName = '';
        let mockCachedBranchMapKeys = [];

        // 模拟API调用
        async function mockQueryRelease(releaseName, forceRefresh = false) {
            testCallCount++;
            console.log(`模拟API调用 #${testCallCount}: queryRelease(${releaseName}, ${forceRefresh})`);
            
            // 模拟返回数据
            const mockData = {
                data: {
                    Task: [
                        {
                            key: "TIC-32923",
                            summary: "测试任务",
                            assignee: "Test User",
                            status: "Done",
                            components: ["TIC Open"]
                        }
                    ]
                },
                branchMap: {
                    "TIC Mall View": ["TIC-32453-release-250812"],
                    "TIC Mall": ["TIC-32762-智能客服优化功能"],
                    "TIC Order": ["TIC-32591_ro订单搜索优化"]
                }
            };

            // 模拟缓存branchMap keys
            if (mockData.branchMap) {
                mockCachedBranchMapKeys = Object.keys(mockData.branchMap);
            }

            return mockData;
        }

        // 测试1: Tab切换
        async function testTabSwitching() {
            const resultDiv = document.getElementById('tab-result');
            resultDiv.innerHTML = '开始测试Tab切换...<br>';
            
            mockCurrentReleaseName = 'TIC生产发布-250812';
            testCallCount = 0;

            // 模拟切换到不同Tab
            const tabs = ['requirements', 'development', 'code-scan', 'test-deploy'];
            
            for (let i = 0; i < tabs.length; i++) {
                const tabName = tabs[i];
                resultDiv.innerHTML += `<span class="info">切换到 ${tabName} Tab...</span><br>`;
                
                // 模拟Tab切换时的数据加载
                if (tabName === 'requirements' || tabName === 'development') {
                    await mockQueryRelease(mockCurrentReleaseName, true);
                    resultDiv.innerHTML += `<span class="success">✓ ${tabName} Tab 调用了API (调用次数: ${testCallCount})</span><br>`;
                } else {
                    resultDiv.innerHTML += `<span class="info">- ${tabName} Tab 暂未实现API调用</span><br>`;
                }
            }
            
            resultDiv.innerHTML += `<br><strong>总结：</strong> 主要Tab切换时都会重新调用API，总调用次数: ${testCallCount}`;
        }

        // 测试2: 版本切换
        async function testVersionSwitching() {
            const version1 = document.getElementById('version1').value;
            const version2 = document.getElementById('version2').value;
            const resultDiv = document.getElementById('version-result');
            
            resultDiv.innerHTML = '开始测试版本切换...<br>';
            testCallCount = 0;

            // 第一次查询
            resultDiv.innerHTML += `<span class="info">查询版本: ${version1}</span><br>`;
            mockCurrentReleaseName = version1;
            await mockQueryRelease(version1, true);
            resultDiv.innerHTML += `<span class="success">✓ 第一次查询完成 (调用次数: ${testCallCount})</span><br>`;

            // 切换版本
            resultDiv.innerHTML += `<span class="info">切换到版本: ${version2}</span><br>`;
            const isVersionChanged = mockCurrentReleaseName !== version2;
            mockCurrentReleaseName = version2;
            
            if (isVersionChanged) {
                resultDiv.innerHTML += `<span class="info">检测到版本变化，清除缓存</span><br>`;
            }

            // 第二次查询
            await mockQueryRelease(version2, true);
            resultDiv.innerHTML += `<span class="success">✓ 版本切换后查询完成 (调用次数: ${testCallCount})</span><br>`;

            resultDiv.innerHTML += `<br><strong>总结：</strong> 版本切换后会重新调用API，总调用次数: ${testCallCount}`;
        }

        // 测试3: branchMap缓存
        async function testBranchMapCache() {
            const version = document.getElementById('test-version-cache').value;
            const resultDiv = document.getElementById('cache-result');
            
            resultDiv.innerHTML = '开始测试branchMap缓存...<br>';
            
            // 调用API
            resultDiv.innerHTML += `<span class="info">调用API查询版本: ${version}</span><br>`;
            const data = await mockQueryRelease(version, true);
            
            // 检查branchMap
            if (data.branchMap) {
                resultDiv.innerHTML += `<span class="success">✓ 获取到branchMap数据</span><br>`;
                resultDiv.innerHTML += `<span class="info">branchMap内容: ${JSON.stringify(data.branchMap, null, 2)}</span><br>`;
                
                // 检查缓存的keys
                resultDiv.innerHTML += `<span class="success">✓ 缓存的keys: [${mockCachedBranchMapKeys.join(', ')}]</span><br>`;
                
                if (mockCachedBranchMapKeys.length > 0) {
                    resultDiv.innerHTML += `<span class="success">✓ branchMap keys缓存成功！</span><br>`;
                } else {
                    resultDiv.innerHTML += `<span class="error">✗ branchMap keys缓存失败</span><br>`;
                }
            } else {
                resultDiv.innerHTML += `<span class="error">✗ 未获取到branchMap数据</span><br>`;
            }
        }

        // 测试4: 完整流程
        async function testCompleteFlow() {
            const resultDiv = document.getElementById('flow-result');
            resultDiv.innerHTML = '开始完整流程测试...<br>';
            testCallCount = 0;

            // 步骤1: 初始查询
            resultDiv.innerHTML += `<span class="info">步骤1: 初始查询版本 TIC生产发布-250812</span><br>`;
            await mockQueryRelease('TIC生产发布-250812', true);
            resultDiv.innerHTML += `<span class="success">✓ 初始查询完成 (调用次数: ${testCallCount})</span><br>`;

            // 步骤2: 切换Tab
            resultDiv.innerHTML += `<span class="info">步骤2: 切换到development Tab</span><br>`;
            await mockQueryRelease('TIC生产发布-250812', true);
            resultDiv.innerHTML += `<span class="success">✓ Tab切换完成 (调用次数: ${testCallCount})</span><br>`;

            // 步骤3: 切换版本
            resultDiv.innerHTML += `<span class="info">步骤3: 切换到版本 TIC生产发布-250813</span><br>`;
            await mockQueryRelease('TIC生产发布-250813', true);
            resultDiv.innerHTML += `<span class="success">✓ 版本切换完成 (调用次数: ${testCallCount})</span><br>`;

            // 步骤4: 再次切换Tab
            resultDiv.innerHTML += `<span class="info">步骤4: 切换到requirements Tab</span><br>`;
            await mockQueryRelease('TIC生产发布-250813', true);
            resultDiv.innerHTML += `<span class="success">✓ 再次Tab切换完成 (调用次数: ${testCallCount})</span><br>`;

            resultDiv.innerHTML += `<br><strong>完整流程测试完成！</strong><br>`;
            resultDiv.innerHTML += `<span class="success">总API调用次数: ${testCallCount} (期望: 4次)</span><br>`;
            resultDiv.innerHTML += `<span class="success">缓存的branchMap keys: [${mockCachedBranchMapKeys.join(', ')}]</span>`;
        }

        // 打开实际页面
        function openRealPage() {
            window.open('./releaseProcess.html', '_blank');
        }
    </script>
</body>
</html>
