// 全局变量
let nodes = [];
let connections = [];
let isDragging = false;
let selectedNode = null;
let offsetX, offsetY;
let startX, startY;
let classMethodMap = {}; // 存储类名和方法名的映射关系
let projectList = []; // 存储工程列表
let interfaceList = []; // 存储接口列表
let classList = []; // 存储类名列表
let methodDetailMap = {}; // 存储方法详细信息的映射关系

// 加载工程列表
async function loadProjectList() {
    try {
        const response = await fetch('../json/gitlabProject.json');
        const data = await response.json();
        
        // 保存工程列表
        projectList = data.map(item => ({
            value: item.projectName,
            text: item.projectName
        }));
        
        // 渲染工程选项
        renderOptions('projectOptions', projectList);
    } catch (error) {
        console.error('加载工程列表失败：', error);
        showError('加载工程列表失败：' + error.message);
    }
}

// 加载接口列表
async function loadInterfaceList(projectName) {
    try {
        // 清空接口列表
        interfaceList = [];
        
        // 禁用输入框
        document.getElementById('interfaceInput').disabled = true;
        document.getElementById('interfaceInput').placeholder = '加载中...';
        
        if (!projectName) return;
        
        const response = await fetch('http://*************:8611/business/api/v1/tools/codeAnalyze/getProjectInterface', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                projectName: projectName
            })
        });

        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        const result = await response.json();
        
        if (result.resultCode !== "0") {
            throw new Error(result.resultMsg || '获取接口列表失败');
        }
        
        // 保存接口列表
        if (result.data && Array.isArray(result.data)) {
            interfaceList = result.data.map(item => ({
                value: item,
                text: item
            }));
        }
        
        // 渲染接口选项
        renderOptions('interfaceOptions', interfaceList);
        
        // 启用输入框
        document.getElementById('interfaceInput').disabled = false;
        document.getElementById('interfaceInput').placeholder = '请选择或输入接口地址';
    } catch (error) {
        console.error('加载接口列表失败：', error);
        document.getElementById('interfaceInput').placeholder = '加载失败';
    }
}

// 加载类和方法列表
async function loadClassMethodList(projectName, className) {
    try {
        // 清空类名列表
        classList = [];
        
        // 禁用输入框
        document.getElementById('classInput').disabled = true;
        document.getElementById('methodInput').disabled = true;
        document.getElementById('classInput').placeholder = '加载中...';
        
        if (!projectName) return;
        
        // 构建请求参数，增加className可选参数
        const requestData = {
            projectName: projectName
        };
        
        // 如果提供了className，添加到请求参数中
        if (className) {
            requestData.className = className;
        }
        
        const response = await fetch('http://*************:8611/business/api/v1/tools/codeAnalyze/getProjectClass', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(requestData)
        });

        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        const result = await response.json();
        
        if (result.resultCode !== "0") {
            throw new Error(result.resultMsg || '获取类和方法列表失败');
        }
        
        if (result.data) {
            // 初始化映射
            classMethodMap = {};
            methodDetailMap = {};
            
            // 处理新的数据结构
            Object.keys(result.data).forEach(className => {
                // 为每个类创建方法名数组
                classMethodMap[className] = [];
                
                // 处理方法详情
                result.data[className].forEach(methodDetail => {
                    // 只存储方法名到classMethodMap
                    classMethodMap[className].push(methodDetail.methodName);
                    
                    // 存储完整方法详情到methodDetailMap
                    const key = `${className}.${methodDetail.methodName}`;
                    methodDetailMap[key] = methodDetail;
                });
            });
            
            // 创建类名列表
            classList = Object.keys(result.data).map(className => ({
                value: className,
                text: className
            }));
            
            // 渲染类名选项
            renderOptions('classOptions', classList);
        }
        
        // 启用类名输入框
        document.getElementById('classInput').disabled = false;
        document.getElementById('classInput').placeholder = '请选择或输入类名';
    } catch (error) {
        console.error('加载类和方法列表失败：', error);
        document.getElementById('classInput').placeholder = '加载失败';
    }
}

// 更新方法列表
function updateMethodList(className) {
    // 清空方法列表
    const methodOptions = document.getElementById('methodOptions');
    methodOptions.innerHTML = '';
    
    // 禁用方法输入框
    document.getElementById('methodInput').disabled = true;
    document.getElementById('methodInput').value = '';
    document.getElementById('methodName').value = '';
    
    if (!className || !classMethodMap[className]) return;
    
    const methods = classMethodMap[className];
    
    // 创建方法列表，显示方法名和参数
    const methodList = methods.map(methodName => {
        // 获取方法详情
        const methodDetail = methodDetailMap[`${className}.${methodName}`];
        
        // 构建显示文本：方法名 + 参数
        let displayText = methodName;
        if (methodDetail && methodDetail.paramStr) {
            displayText += `(${methodDetail.paramStr})`;
        }
        
        return {
            value: methodName, // 值仍然只是方法名
            text: displayText   // 显示文本包含方法名和参数
        };
    });
    
    // 渲染方法选项
    renderOptions('methodOptions', methodList);
    
    // 启用方法输入框
    document.getElementById('methodInput').disabled = false;
}

// 渲染选项列表
function renderOptions(containerId, options) {
    const container = document.getElementById(containerId);
    container.innerHTML = '';
    
    options.forEach(option => {
        const optionElement = document.createElement('div');
        optionElement.className = 'option';
        optionElement.dataset.value = option.value;
        optionElement.textContent = option.text;
        container.appendChild(optionElement);
    });
}

// 过滤选项
function filterOptions(containerId, filterText) {
    const container = document.getElementById(containerId);
    const options = container.querySelectorAll('.option');
    
    let hasVisibleOptions = false;
    
    options.forEach(option => {
        const text = option.textContent.toLowerCase();
        const filter = filterText.toLowerCase();
        
        if (text.includes(filter)) {
            option.style.display = '';
            hasVisibleOptions = true;
        } else {
            option.style.display = 'none';
        }
    });
    
    return hasVisibleOptions;
}

// 分析代码
async function analyzeCode() {
    try {
        const resultDiv = document.getElementById('result');
        const graphContainer = document.getElementById('graph-container');
        const projectName = document.getElementById('projectName').value.trim();
        const queryType = document.querySelector('input[name="queryType"]:checked')?.value;
        
        let requestData = {
            projectName: projectName
        };
        
        // 根据查询类型设置请求参数
        if (queryType === 'interface') {
            const interfaceUrl = document.getElementById('interfaceUrl').value.trim();
            if (!interfaceUrl) {
                throw new Error('请选择接口地址');
            }
            requestData.interfaceRoute = interfaceUrl;
        } else if (queryType === 'method') {
            const className = document.getElementById('className').value.trim();
            const methodName = document.getElementById('methodName').value.trim();
            if (!className) {
                throw new Error('请选择类名');
            }
            // 添加类名到请求参数
            requestData.className = className;
            
            // 如果有方法名，则添加到请求参数（方法名变为可选）
            if (methodName) {
                requestData.methodName = methodName;
            }
        } else {
            throw new Error('请选择查询类型');
        }
        
        // 显示加载中
        graphContainer.innerHTML = '<div class="loading">分析中...</div>';
        
        // 调用接口获取分析结果
        const response = await fetch('http://*************:8611/business/api/v1/tools/codeAnalyze/getAnalyzeResult', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(requestData)
        });

        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        const result = await response.json();
        
        // 添加调试信息
        console.log('API返回数据:', result);
        
        if (result.resultCode !== "0") {
            throw new Error(result.resultMsg || '查询失败');
        }
        
        // 清空容器
        graphContainer.innerHTML = '';
        
        // 渲染图形
        renderGraph(result.data, graphContainer);
        
    } catch (error) {
        console.error('代码分析失败：', error);
        showError('代码分析失败：' + error.message);
    }
}

// 渲染图形
function renderGraph(data, container) {
    // 重置全局变量
    nodes = [];
    connections = [];
    
    // 如果没有数据，显示提示
    if (!data || data.length === 0) {
        container.innerHTML = '<div class="error">没有找到相关代码分析数据</div>';
        return;
    }
    
    try {
        console.log('开始渲染图形，数据结构:', data);
        
        // 创建一个内部容器，用于放置节点
        const innerContainer = document.createElement('div');
        innerContainer.className = 'inner-graph-container';
        innerContainer.style.position = 'relative';
        innerContainer.style.width = '3000px'; // 设置足够大的初始宽度
        innerContainer.style.height = '2000px'; // 设置足够大的初始高度
        container.appendChild(innerContainer);
        
        // 递归处理节点
        processNodes(data, innerContainer, 0, 50, 100);
        
        // 调整内部容器大小以适应所有节点
        adjustContainerSize(innerContainer);
        
        // 绘制连接线
        drawConnections();
        
        // 添加事件监听
        setupEventListeners();
        
        console.log('图形渲染完成，节点数:', nodes.length, '连接数:', connections.length);
    } catch (error) {
        console.error('渲染图形失败:', error);
        container.innerHTML = `<div class="error">渲染图形失败: ${error.message}</div>`;
    }
}

// 调整容器大小以适应所有节点
function adjustContainerSize(container) {
    if (nodes.length === 0) return;
    
    // 找出所有节点的最大x和y坐标
    let maxX = 0;
    let maxY = 0;
    
    nodes.forEach(node => {
        const rect = node.element.getBoundingClientRect();
        const right = node.x + rect.width;
        const bottom = node.y + rect.height;
        
        if (right > maxX) maxX = right;
        if (bottom > maxY) maxY = bottom;
    });
    
    // 添加一些边距
    maxX += 100;
    maxY += 100;
    
    // 设置容器大小
    container.style.width = `${maxX}px`;
    container.style.height = `${maxY}px`;
}

// 递归处理节点
function processNodes(nodeData, container, level, startX, startY) {
    if (!nodeData || nodeData.length === 0) return;
    
    const horizontalSpacing = 300;
    const verticalSpacing = 150; // 减小垂直间距
    
    const levelNodes = [];
    
    nodeData.forEach((item, index) => {
        // 创建节点
        const node = createNode(item, level);
        
        // 设置节点位置
        const x = startX + level * horizontalSpacing;
        const y = startY + index * verticalSpacing;
        
        node.style.left = `${x}px`;
        node.style.top = `${y}px`;
        
        // 添加到容器
        container.appendChild(node);
        
        // 保存节点信息
        const nodeInfo = {
            element: node,
            data: item,
            x: x,
            y: y,
            level: level
        };
        
        nodes.push(nodeInfo);
        levelNodes.push(nodeInfo);
        
        // 处理子节点 - 使用codeTree字段
        if (item.codeTree && Array.isArray(item.codeTree) && item.codeTree.length > 0) {
            // 递归处理子节点
            const childNodes = processNodes(item.codeTree, container, level + 1, startX, startY + index * verticalSpacing);
            
            // 创建连接
            if (childNodes && childNodes.length > 0) {
                childNodes.forEach((childNode, childIndex) => {
                    connections.push({
                        from: node,
                        to: childNode.element,
                        order: childIndex + 1 // 添加序号，从1开始
                    });
                });
            }
        }
    });
    
    return levelNodes;
}

// 创建节点
function createNode(data, level) {
    const node = document.createElement('div');
    node.className = 'node';
    node.dataset.level = level;
    
    // 节点头部 - 只显示类名的最后部分
    const header = document.createElement('div');
    header.className = 'node-header';
    
    // 提取类名的最后部分
    let displayName = '';
    if (data.className) {
        const classNameParts = data.className.split('.');
        const shortClassName = classNameParts[classNameParts.length - 1];
        displayName = `${shortClassName}.${data.methodName}`;
    } else {
        displayName = data.methodName || '未知方法';
    }
    
    header.textContent = displayName;
    header.title = data.className ? `${data.className}.${data.methodName}` : data.methodName; // 添加完整类名作为提示
    
    // 节点内容
    const content = document.createElement('div');
    content.className = 'node-content';
    
    // 添加接口信息（如果有）
    if (data.interfaceRoute) {
        const route = document.createElement('div');
        route.textContent = `接口: ${data.interfaceRoute}`;
        content.appendChild(route);
    }
    
    // 添加请求参数信息 - 使用requestParams
    if (data.requestParams && Array.isArray(data.requestParams) && data.requestParams.length > 0) {
        const paramsContainer = document.createElement('div');
        paramsContainer.textContent = '参数:';
        content.appendChild(paramsContainer);
        
        // 为每个参数创建一个项目
        data.requestParams.forEach(param => {
            if (param && typeof param === 'object') {
                const paramItem = document.createElement('div');
                paramItem.className = 'param-item';
                
                let paramText = '';
                if (param.paramType) {
                    paramText += `类型: ${param.paramType}, `;
                }
                if (param.paramName) {
                    paramText += `参数: ${param.paramName}`;
                }
                
                paramItem.textContent = paramText || '未知参数';
                content.appendChild(paramItem);
            }
        });
    } else {
        const paramsContainer = document.createElement('div');
        paramsContainer.textContent = '参数: 无';
        content.appendChild(paramsContainer);
    }
    
    node.appendChild(header);
    node.appendChild(content);
    
    // 存储完整数据，包括id
    node.dataset.nodeData = JSON.stringify(data);
    if (data.id || data.codeId) {
        node.dataset.id = data.id || data.codeId;
    }
    
    return node;
}

// 绘制连接线
function drawConnections() {
    const container = document.querySelector('.inner-graph-container') || document.getElementById('graph-container');
    
    // 移除现有的连接线
    const existingLines = container.querySelectorAll('.connections-svg');
    existingLines.forEach(line => line.remove());
    
    // 创建SVG容器
    const svgContainer = document.createElementNS('http://www.w3.org/2000/svg', 'svg');
    svgContainer.classList.add('connections-svg');
    svgContainer.style.position = 'absolute';
    svgContainer.style.top = '0';
    svgContainer.style.left = '0';
    svgContainer.style.width = '100%';
    svgContainer.style.height = '100%';
    svgContainer.style.pointerEvents = 'none';
    svgContainer.style.zIndex = '1'; // SVG层级
    
    console.log('连接数量:', connections.length); // 调试信息
    
    // 添加箭头定义 - 美化箭头
    const defs = document.createElementNS('http://www.w3.org/2000/svg', 'defs');
    const marker = document.createElementNS('http://www.w3.org/2000/svg', 'marker');
    marker.setAttribute('id', 'arrowhead');
    marker.setAttribute('markerWidth', '10');
    marker.setAttribute('markerHeight', '7');
    marker.setAttribute('refX', '9');
    marker.setAttribute('refY', '3.5');
    marker.setAttribute('orient', 'auto');
    
    // 使用更美观的箭头形状
    const polygon = document.createElementNS('http://www.w3.org/2000/svg', 'polygon');
    polygon.setAttribute('points', '0 0, 10 3.5, 0 7');
    polygon.setAttribute('fill', '#4CAF50');
    polygon.setAttribute('stroke', '#2E7D32');
    polygon.setAttribute('stroke-width', '0.5');
    
    marker.appendChild(polygon);
    defs.appendChild(marker);
    svgContainer.appendChild(defs);
    
    // 绘制新的连接线
    connections.forEach((connection, index) => {
        try {
            // 获取节点位置
            const fromRect = connection.from.getBoundingClientRect();
            const toRect = connection.to.getBoundingClientRect();
            const containerRect = container.getBoundingClientRect();
            
            // 计算相对于容器的位置
            const fromX = parseFloat(connection.from.style.left) + connection.from.offsetWidth / 2;
            const fromY = parseFloat(connection.from.style.top) + connection.from.offsetHeight;
            const toX = parseFloat(connection.to.style.left) + connection.to.offsetWidth / 2;
            const toY = parseFloat(connection.to.style.top);
            
            // 创建路径
            const path = document.createElementNS('http://www.w3.org/2000/svg', 'path');
            
            // 计算控制点（简单的贝塞尔曲线）
            const controlX1 = fromX;
            const controlY1 = fromY + 40;
            const controlX2 = toX;
            const controlY2 = toY - 40;
            
            // 设置路径 - 使用三次贝塞尔曲线
            path.setAttribute('d', `M ${fromX} ${fromY} C ${controlX1} ${controlY1}, ${controlX2} ${controlY2}, ${toX} ${toY}`);
            path.setAttribute('stroke', '#4CAF50');
            path.setAttribute('stroke-width', '2');
            path.setAttribute('fill', 'none');
            path.setAttribute('marker-end', 'url(#arrowhead)');
            
            // 添加渐变效果
            path.setAttribute('stroke-linecap', 'round');
            path.setAttribute('stroke-linejoin', 'round');
            
            svgContainer.appendChild(path);
            
            // 如果有序号，添加序号标签
            if (connection.order !== undefined) {
                // 计算序号标签的位置（在曲线的中点附近）
                const midX = (fromX + toX) / 2;
                const midY = (fromY + toY) / 2;
                
                // 创建序号标签
                const orderLabel = document.createElementNS('http://www.w3.org/2000/svg', 'g');
                
                // 创建圆形背景 - 添加阴影效果
                const circle = document.createElementNS('http://www.w3.org/2000/svg', 'circle');
                circle.setAttribute('cx', midX);
                circle.setAttribute('cy', midY);
                circle.setAttribute('r', '10');
                circle.setAttribute('fill', '#4CAF50');
                circle.setAttribute('filter', 'url(#shadow)');
                
                // 创建文本
                const text = document.createElementNS('http://www.w3.org/2000/svg', 'text');
                text.setAttribute('x', midX);
                text.setAttribute('y', midY);
                text.setAttribute('text-anchor', 'middle');
                text.setAttribute('dominant-baseline', 'middle');
                text.setAttribute('fill', 'white');
                text.setAttribute('font-size', '12px');
                text.setAttribute('font-weight', 'bold');
                text.textContent = connection.order;
                
                // 添加到SVG
                orderLabel.appendChild(circle);
                orderLabel.appendChild(text);
                svgContainer.appendChild(orderLabel);
            }
        } catch (error) {
            console.error(`绘制第${index}条连接线时出错:`, error);
        }
    });
    
    // 添加阴影滤镜
    const filter = document.createElementNS('http://www.w3.org/2000/svg', 'filter');
    filter.setAttribute('id', 'shadow');
    filter.setAttribute('x', '-20%');
    filter.setAttribute('y', '-20%');
    filter.setAttribute('width', '140%');
    filter.setAttribute('height', '140%');
    
    const feDropShadow = document.createElementNS('http://www.w3.org/2000/svg', 'feDropShadow');
    feDropShadow.setAttribute('dx', '0');
    feDropShadow.setAttribute('dy', '1');
    feDropShadow.setAttribute('stdDeviation', '2');
    feDropShadow.setAttribute('flood-color', 'rgba(0,0,0,0.3)');
    
    filter.appendChild(feDropShadow);
    defs.appendChild(filter);
    
    // 将SVG添加到容器
    container.appendChild(svgContainer);
    
    // 确保节点在连接线之上
    nodes.forEach(node => {
        node.element.style.zIndex = '2';
    });
}

// 设置事件监听
function setupEventListeners() {
    const container = document.getElementById('graph-container');
    const nodes = container.querySelectorAll('.node');
    
    nodes.forEach(node => {
        // 移除点击事件，改为在mouseup中处理
        // 这样可以确保拖动后不会触发点击事件
        
        // 拖动节点
        node.addEventListener('mousedown', function(e) {
            if (e.button !== 0) return; // 只响应左键
            
            isDragging = false; // 重置拖动状态
            selectedNode = this;
            
            const rect = selectedNode.getBoundingClientRect();
            const containerRect = container.getBoundingClientRect();
            
            // 计算偏移量
            offsetX = e.clientX - rect.left;
            offsetY = e.clientY - rect.top;
            
            // 记录起始位置
            startX = e.clientX;
            startY = e.clientY;
            
            // 添加临时事件监听
            document.addEventListener('mousemove', onMouseMove);
            document.addEventListener('mouseup', onMouseUp);
            
            e.preventDefault();
            e.stopPropagation();
        });
    });
    
    // 点击容器空白处关闭详情
    container.addEventListener('click', function(e) {
        if (e.target === container) {
            closeNodeDetail();
        }
    });
}

// 鼠标移动处理
function onMouseMove(e) {
    if (!selectedNode) return;
    
    // 判断是否达到拖动阈值
    const moveX = Math.abs(e.clientX - startX);
    const moveY = Math.abs(e.clientY - startY);
    
    // 只要移动超过5像素就标记为拖动
    if (moveX > 5 || moveY > 5) {
        isDragging = true;
        // 添加拖动样式类，提供视觉反馈
        selectedNode.classList.add('dragging');
    }
    
    if (isDragging) {
        const container = document.getElementById('graph-container');
        const containerRect = container.getBoundingClientRect();
        
        // 计算新位置（相对于容器）
        const x = e.clientX - containerRect.left - offsetX + container.scrollLeft;
        const y = e.clientY - containerRect.top - offsetY + container.scrollTop;
        
        // 更新节点位置
        selectedNode.style.left = `${x}px`;
        selectedNode.style.top = `${y}px`;
        
        // 重绘连接线
        drawConnections();
    }
}

// 鼠标释放处理
function onMouseUp(e) {
    // 移除临时事件监听
    document.removeEventListener('mousemove', onMouseMove);
    document.removeEventListener('mouseup', onMouseUp);
    
    // 如果有选中的节点，移除拖动样式
    if (selectedNode) {
        selectedNode.classList.remove('dragging');
        
        // 关键修复：如果发生了拖动，不显示详情
        // 只有纯点击（没有拖动）才显示详情
        if (!isDragging) {
            showNodeDetail(selectedNode);
        }
    }
    
    // 重置状态
    isDragging = false;
    selectedNode = null;
}

// 显示节点详情
function showNodeDetail(node) {
    // 关闭已有的详情
    closeNodeDetail();
    
    // 解析节点数据
    const nodeData = JSON.parse(node.dataset.nodeData);
    
    // 创建遮罩
    const overlay = document.createElement('div');
    overlay.className = 'overlay';
    overlay.addEventListener('click', closeNodeDetail);
    
    // 创建详情面板
    const detail = document.createElement('div');
    detail.className = 'node-detail';
    detail.onclick = e => e.stopPropagation();
    
    // 详情头部
    const header = document.createElement('div');
    header.className = 'node-detail-header';
    
    const title = document.createElement('h3');
    title.textContent = nodeData.className ? `${nodeData.className}.${nodeData.methodName}` : nodeData.methodName;
    
    const closeBtn = document.createElement('span');
    closeBtn.className = 'node-detail-close';
    closeBtn.textContent = '×';
    closeBtn.onclick = closeNodeDetail;
    
    header.appendChild(title);
    header.appendChild(closeBtn);
    
    // 详情内容
    const content = document.createElement('div');
    content.className = 'node-detail-content';
    
    // 添加项目信息
    if (nodeData.project) {
        const project = document.createElement('p');
        project.innerHTML = `<strong>项目:</strong> ${nodeData.project}`;
        content.appendChild(project);
    }
    
    // 添加ID信息
    if (nodeData.id || nodeData.codeId) {
        const idElement = document.createElement('p');
        idElement.innerHTML = `<strong>代码ID:</strong> ${nodeData.id || nodeData.codeId}`;
        content.appendChild(idElement);
    }
    
    // 添加接口信息
    if (nodeData.interfaceRoute) {
        const route = document.createElement('p');
        route.innerHTML = `<strong>接口路径:</strong> ${nodeData.interfaceRoute}`;
        content.appendChild(route);
    }
    
    // 添加请求参数信息
    if (nodeData.requestParams && Array.isArray(nodeData.requestParams) && nodeData.requestParams.length > 0) {
        const paramsTitle = document.createElement('p');
        paramsTitle.innerHTML = `<strong>请求参数:</strong>`;
        content.appendChild(paramsTitle);
        
        const paramsList = document.createElement('ul');
        nodeData.requestParams.forEach(param => {
            if (param && typeof param === 'object') {
                const paramItem = document.createElement('li');
                let paramText = '';
                
                if (param.paramType) {
                    paramText += `类型: ${param.paramType}`;
                }
                
                if (param.paramName) {
                    if (paramText) paramText += ', ';
                    paramText += `参数: ${param.paramName}`;
                }
                
                paramItem.textContent = paramText || '未知参数';
                paramsList.appendChild(paramItem);
            }
        });
        content.appendChild(paramsList);
    } else {
        const noParams = document.createElement('p');
        noParams.innerHTML = `<strong>请求参数:</strong> 无`;
        content.appendChild(noParams);
    }
    
    // 添加代码信息
    if (nodeData.code) {
        const codeTitle = document.createElement('p');
        codeTitle.innerHTML = `<strong>代码:</strong>`;
        content.appendChild(codeTitle);
        
        const code = document.createElement('pre');
        code.className = 'node-detail-code';
        code.textContent = nodeData.code;
        content.appendChild(code);
    }
    
    // 添加AI代码分析按钮和结果区域
    const aiAnalysisContainer = document.createElement('div');
    aiAnalysisContainer.className = 'ai-analysis-container';
    
    const aiButton = document.createElement('button');
    aiButton.className = 'ai-analysis-button';
    aiButton.innerHTML = '<i class="ai-icon">🤖</i> AI代码分析';
    aiButton.onclick = () => analyzeCodeWithAI(nodeData);
    
    const aiResultContainer = document.createElement('div');
    aiResultContainer.className = 'ai-result-container';
    aiResultContainer.id = 'aiResultContainer';
    aiResultContainer.style.display = 'none';
    
    aiAnalysisContainer.appendChild(aiButton);
    aiAnalysisContainer.appendChild(aiResultContainer);
    content.appendChild(aiAnalysisContainer);
    
    // 组装详情面板
    detail.appendChild(header);
    detail.appendChild(content);
    
    // 添加到文档
    document.body.appendChild(overlay);
    document.body.appendChild(detail);
    
    // 添加样式
    document.head.insertAdjacentHTML('beforeend', `
    <style>
        .ai-analysis-button {
            display: flex;
            align-items: center;
            justify-content: center;
            background: linear-gradient(135deg, #4CAF50, #2E7D32);
            color: white;
            border: none;
            border-radius: 4px;
            padding: 10px 16px;
            font-size: 14px;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 2px 5px rgba(0,0,0,0.2);
            margin-top: 15px;
            font-weight: 500;
        }
        
        .ai-analysis-button:hover {
            background: linear-gradient(135deg, #43A047, #2E7D32);
            box-shadow: 0 4px 8px rgba(0,0,0,0.3);
            transform: translateY(-1px);
        }
        
        .ai-analysis-button:active {
            transform: translateY(1px);
            box-shadow: 0 1px 3px rgba(0,0,0,0.2);
        }
        
        .ai-icon {
            margin-right: 8px;
            font-size: 16px;
        }
        
        .ai-result-container {
            margin-top: 15px;
            padding: 15px;
            background-color: #f5f5f5;
            border-radius: 4px;
            border-left: 4px solid #4CAF50;
            max-height: 300px;
            overflow-y: auto;
        }
        
        .ai-result {
            white-space: pre-wrap;
            font-family: monospace;
            font-size: 13px;
            line-height: 1.5;
            color: #333;
        }
    </style>
    `);
}

// 关闭节点详情
function closeNodeDetail() {
    const overlay = document.querySelector('.overlay');
    const detail = document.querySelector('.node-detail');
    
    if (overlay) overlay.remove();
    if (detail) detail.remove();
}

// 显示错误信息
function showError(message) {
    const graphContainer = document.getElementById('graph-container');
    graphContainer.innerHTML = `<div class="error">${message}</div>`;
}

// 初始化可搜索下拉框
function initSearchableSelect(inputId, optionsId, hiddenId, placeholder) {
    const input = document.getElementById(inputId);
    const options = document.getElementById(optionsId);
    const hidden = document.getElementById(hiddenId);
    
    // 设置初始占位符
    input.placeholder = placeholder;
    
    // 输入框获得焦点时显示选项
    input.addEventListener('focus', () => {
        options.style.display = 'block';
    });
    
    // 输入框输入时过滤选项
    input.addEventListener('input', () => {
        const hasOptions = filterOptions(optionsId, input.value);
        options.style.display = hasOptions ? 'block' : 'none';
        
        // 清空隐藏字段
        hidden.value = '';
    });
    
    // 点击选项时设置值
    options.addEventListener('click', (e) => {
        if (e.target.classList.contains('option')) {
            const value = e.target.dataset.value;
            const text = e.target.textContent;
            
            // 对于方法输入框，显示完整文本（包含参数），但值只保存方法名
            if (hiddenId === 'methodName') {
                input.value = text; // 显示完整文本
                hidden.value = value; // 保存方法名
            } else {
                input.value = text;
                hidden.value = value;
            }
            
            options.style.display = 'none';
            
            // 如果是类名，更新方法列表
            if (hiddenId === 'className') {
                updateMethodList(value);
            }
            
            // 触发change事件
            const event = new Event('change');
            hidden.dispatchEvent(event);
        }
    });
    
    // 点击文档其他地方时隐藏选项
    document.addEventListener('click', (e) => {
        if (!input.contains(e.target) && !options.contains(e.target)) {
            options.style.display = 'none';
        }
    });
}

// 表单事件处理
document.addEventListener('DOMContentLoaded', () => {
    // 加载工程列表
    loadProjectList();
    
    // 初始化可搜索下拉框
    initSearchableSelect('projectInput', 'projectOptions', 'projectName', '请选择工程');
    initSearchableSelect('interfaceInput', 'interfaceOptions', 'interfaceUrl', '请选择或输入接口地址');
    initSearchableSelect('classInput', 'classOptions', 'className', '请选择或输入类名');
    initSearchableSelect('methodInput', 'methodOptions', 'methodName', '请选择或输入方法名（可选）'); // 修改提示文本
    
    // 工程选择变化事件
    document.getElementById('projectName').addEventListener('change', function() {
        const projectName = this.value.trim();
        
        // 重置查询类型
        document.querySelectorAll('input[name="queryType"]').forEach(radio => {
            radio.checked = false;
        });
        
        // 隐藏所有查询类型相关的行
        document.getElementById('interfaceRow').style.display = 'none';
        document.getElementById('classMethodRow').style.display = 'none';
        
        // 清空并禁用输入框
        document.getElementById('interfaceInput').value = '';
        document.getElementById('interfaceUrl').value = '';
        document.getElementById('classInput').value = '';
        document.getElementById('className').value = '';
        document.getElementById('methodInput').value = '';
        document.getElementById('methodName').value = '';
        
        document.getElementById('interfaceInput').disabled = true;
        document.getElementById('classInput').disabled = true;
        document.getElementById('methodInput').disabled = true;
    });
    
    // 类名输入框变化事件 - 添加新的事件监听器
    document.getElementById('className').addEventListener('change', function() {
        const projectName = document.getElementById('projectName').value.trim();
        const className = this.value.trim();
        
        if (projectName && className) {
            // 当类名变化时，使用className参数重新加载类和方法列表
            loadClassMethodList(projectName, className);
        }
        
        // 更新方法列表
        updateMethodList(className);
    });
    
    // 查询类型变化事件
    document.querySelectorAll('input[name="queryType"]').forEach(radio => {
        radio.addEventListener('change', function() {
            const queryType = this.value;
            const projectName = document.getElementById('projectName').value.trim();
            
            // 隐藏所有查询类型相关的行
            document.getElementById('interfaceRow').style.display = 'none';
            document.getElementById('classMethodRow').style.display = 'none';
            
            // 根据查询类型显示相应的行
            if (queryType === 'interface') {
                document.getElementById('interfaceRow').style.display = 'flex';
                if (projectName) {
                    loadInterfaceList(projectName);
                }
            } else if (queryType === 'method') {
                document.getElementById('classMethodRow').style.display = 'flex';
                if (projectName) {
                    // 不传className参数，加载所有类
                    loadClassMethodList(projectName);
                }
            }
        });
    });
    
    // 表单提交事件
    document.getElementById('analyzeForm').addEventListener('submit', async (event) => {
        event.preventDefault();
        
        const projectName = document.getElementById('projectName').value.trim();
        const queryType = document.querySelector('input[name="queryType"]:checked')?.value;

        if (!projectName) {
            showError('请选择工程');
            return;
        }
        
        if (!queryType) {
            showError('请选择查询类型');
            return;
        }
        
        if (queryType === 'interface') {
            const interfaceUrl = document.getElementById('interfaceUrl').value.trim();
            if (!interfaceUrl) {
                showError('请选择接口地址');
                return;
            }
        } else if (queryType === 'method') {
            const className = document.getElementById('className').value.trim();
            // 只验证类名，不再验证方法名
            if (!className) {
                showError('请选择类名');
                return;
            }
            // 方法名不再是必填项，移除验证
        }

        try {
            await analyzeCode();
        } catch (error) {
            showError('分析失败: ' + error.message);
        }
    });
});

// 添加拖动样式
document.head.insertAdjacentHTML('beforeend', `
<style>
.node.dragging {
    opacity: 0.8;
    cursor: grabbing;
    z-index: 100;
}
</style>
`);

// AI代码分析函数
async function analyzeCodeWithAI(nodeData) {
    const resultContainer = document.getElementById('aiResultContainer');
    resultContainer.style.display = 'block';
    resultContainer.innerHTML = '<div class="loading">分析中...</div>';
    
    try {
        // 构建请求消息
        const codeId = nodeData.id || nodeData.codeId || '未知';
        const message = `分析代码，代码ID为${codeId}`;
        
        // 调用API
        const response = await fetch('http://*************:7101/business/code/v1/analyseSnippets?message=' + encodeURIComponent(message), {
            method: 'GET',
            headers: {
                'Accept': 'text/plain'
            }
        });
        
        if (!response.ok) {
            throw new Error(`请求失败: ${response.status}`);
        }
        
        // 获取分析结果
        const result = await response.text();
        
        // 格式化结果文本，确保换行正确显示
        const formattedResult = formatAIResult(result);
        
        // 显示结果
        resultContainer.innerHTML = `<div class="ai-result">${formattedResult}</div>`;
    } catch (error) {
        console.error('AI代码分析失败:', error);
        resultContainer.innerHTML = `<div class="error">分析失败: ${error.message}</div>`;
    }
}

// 格式化AI结果文本
function formatAIResult(text) {
    if (!text) return '';
    
    // 替换换行符
    let formatted = text.replace(/\n/g, '<br>');
    
    // 添加段落分隔
    formatted = formatted.replace(/\.\s+(?=[A-Z])/g, '.<br><br>');
    
    // 处理列表项
    formatted = formatted.replace(/(\d+\.\s+)/g, '<br>$1');
    
    // 处理代码块
    formatted = formatted.replace(/```([\s\S]*?)```/g, '<pre class="code-block">$1</pre>');
    
    return formatted;
}
