// 创建悬浮按钮和模态框
function createElements() {
    // 检查是否已存在元素
    if (document.getElementById('vm-query-button')) {
        return;
    }

    // 创建悬浮按钮
    const button = document.createElement('button');
    button.id = 'vm-query-button';
    
    // 添加SGS图标
    const img = document.createElement('img');
    img.src = chrome.runtime.getURL('images/sgs-icon.png');
    img.alt = 'SGS';
    img.onerror = () => {
        console.warn('图标加载失败，使用默认文本');
        img.style.display = 'none';
        button.textContent = 'SGS';
    };
    button.appendChild(img);
    
    // 创建浮动菜单
    const menu = document.createElement('div');
    menu.className = 'floating-menu';
    
    // 添加菜单项
    const menuItems = [
        { 
            icon: '🔧', 
            text: '日常运维',
            subMenu: [
                {
                    icon: '🔍', 
                    text: '查询主机资源', 
                    action: 'query'
                },
                {
                    icon: '📊', 
                    text: 'AI分析日志',
                    action: 'analyze'
                },
                {
                    icon: '🧩', 
                    text: '代码分析',
                    action: 'code-analyze',
                    url: chrome.runtime.getURL('analyze/codeAnalyze.html')
                },
                {
                    icon: '🌐', 
                    text: 'TIC网站',
                    action: 'tic-website'
                }
            ]
        },
        {
            icon: '🤖', 
            text: 'AI MCP',
            subMenu: [
                {
                    icon: '🤖', 
                    text: 'AI运维',
                    action: 'ai-ops',
                    url: chrome.runtime.getURL('ai-ops/aiOps.html')
                }
            ]
        },
        {
            icon: '📦',
            text: '工具',
            subMenu: [
              {
                  icon: '📝',
                  text: 'JSON转换工具',
                  action: 'json-editor',
                  url: 'http://10.169.128.35:5175/editor'
              }
            ]
        },
        {
            icon: '🚀',
            text: '版本发布',
            subMenu: [
                {
                    icon: '📤',
                    text: '发布平台',
                    action: 'release-platform',
                    url: 'http://10.168.129.105:8000/#/workfllow/orders'
                },
                {
                    icon: '📋',
                    text: 'Release查询',
                    action: 'release'
                },
                {
                	icon: '📦',
                    text: '大版本发布',
                    action: 'bigRelease'
                },
                {
                    icon: '🔄',
                    text: 'release发布流程',
                    action: 'release-process'
                }
            ]
        },
        { 
            icon: '📊',
            text: 'Sprint管理',
            subMenu: [
                {
                    icon: '⏱️',
                    text: '当前Sprint工时',
                    action: 'sprint-time',
                    url: chrome.runtime.getURL('sprint/sprintTime.html')
                }
            ]
        }
    ];
    
    menuItems.forEach(item => {
        const menuItem = document.createElement('div');
        menuItem.className = 'floating-menu-item';
        
        // 创建内容容器，以便更好地控制布局
        const contentSpan = document.createElement('span');
        contentSpan.innerHTML = `<i>${item.icon}</i>${item.text}`;
        menuItem.appendChild(contentSpan);
        
        if (item.subMenu && item.subMenu.length > 0) {
            // 添加子菜单指示器
            const indicator = document.createElement('span');
            indicator.className = 'submenu-indicator';
            indicator.textContent = '◀'; // 改为左箭头
            menuItem.appendChild(indicator);
            
            // 标记为有子菜单的项
            menuItem.classList.add('has-submenu');
            
            // 添加直接的点击事件处理
            menuItem.addEventListener('click', (e) => {
                // 阻止事件冒泡
                e.stopPropagation();
                
                // 获取所有子菜单
                const allSubMenus = document.querySelectorAll('.floating-submenu');
                
                // 创建一个新的子菜单元素
                const newSubMenu = document.createElement('div');
                newSubMenu.className = 'floating-submenu active-submenu';
                newSubMenu.style.display = 'block';
                newSubMenu.style.position = 'fixed';
                newSubMenu.style.zIndex = '2147483647';
                newSubMenu.style.backgroundColor = 'white';
                newSubMenu.style.border = '1px solid #e0e0e0';
                newSubMenu.style.borderRadius = '6px';
                newSubMenu.style.boxShadow = '0 2px 10px rgba(0,0,0,0.2)';
                newSubMenu.style.padding = '5px 0';
                newSubMenu.style.minWidth = '150px';
                
                // 获取父菜单项的位置
                const menuRect = menuItem.getBoundingClientRect();
                
                // 计算子菜单的位置 - 放在父菜单项的左侧
                const left = menuRect.left - 160; // 子菜单宽度约为150px，加上一些间距
                const top = menuRect.top;
                
                // 设置子菜单位置
                newSubMenu.style.left = `${left}px`;
                newSubMenu.style.top = `${top}px`;
                
                // 添加子菜单项
                item.subMenu.forEach(subItem => {
                    const subMenuItem = document.createElement('div');
                    subMenuItem.className = 'floating-menu-item';
                    subMenuItem.innerHTML = `<i>${subItem.icon}</i>${subItem.text}`;
                    subMenuItem.dataset.action = subItem.action;
                    if (subItem.url) {
                        subMenuItem.dataset.url = subItem.url;
                    }
                    
                    // 添加点击事件
                    subMenuItem.addEventListener('click', (subEvent) => {
                        subEvent.stopPropagation();
                        
                        // 处理子菜单项点击
                        const action = subItem.action;
                        const currentTop = button.offsetTop; // 使用当前按钮的位置
                        
                        switch(action) {
                            case 'query':
                                const modal = document.getElementById('vm-query-modal');
                                modal.style.top = currentTop + 'px';
                                modal.style.transform = 'none';
                                modal.style.display = 'block';
                                const iframe = modal.querySelector('iframe');
                                iframe.src = chrome.runtime.getURL('tools/popup.html');
                                break;
                            case 'analyze':
                                const analyzeModal = document.getElementById('vm-query-modal');
                                analyzeModal.style.top = currentTop + 'px';
                                analyzeModal.style.transform = 'none';
                                analyzeModal.style.display = 'block';
                                const analyzeIframe = analyzeModal.querySelector('iframe');
                                analyzeIframe.src = chrome.runtime.getURL('analyze/analyze.html');
                                break;
                            case 'tic-website':
                                const websiteModal = document.getElementById('vm-query-modal');
                                websiteModal.style.top = currentTop + 'px';
                                websiteModal.style.transform = 'none';
                                websiteModal.style.display = 'block';
                                const websiteIframe = websiteModal.querySelector('iframe');
                                websiteIframe.src = chrome.runtime.getURL('tools/ticWebsite.html');
                                break;
                            case 'ai-ops':
                                window.open(subItem.url, '_blank');
                                break;
                            case 'json-editor':
                                window.open(subItem.url, '_blank');
                                break;
                            case 'release-platform':
                                window.open(subItem.url, '_blank');
                                break;
                            case 'release':
                                window.open(chrome.runtime.getURL('release/release.html'), '_blank');
                                break;
                            case 'bigRelease':
                                window.open(chrome.runtime.getURL('release/bigRelease.html'), '_blank');
                                break;
                            case 'release-process':
                                window.open(chrome.runtime.getURL('release/releaseProcess.html'), '_blank');
                                break;
                            case 'sprint-time':
                                window.open(subItem.url, '_blank');
                                break;
                            case 'code-analyze':
                                window.open(subItem.url, '_blank');
                                break;

                        }
                        
                        // 移除子菜单
                        document.body.removeChild(newSubMenu);
                        
                        // 隐藏浮动菜单
                        const floatingMenu = document.querySelector('.floating-menu');
                        if (floatingMenu) floatingMenu.classList.remove('show');
                    });
                    
                    newSubMenu.appendChild(subMenuItem);
                });

                // 移除所有现有的活动子菜单
                document.querySelectorAll('.active-submenu').forEach(menu => {
                    if (menu.parentNode) {
                        menu.parentNode.removeChild(menu);
                    }
                });
                
                // 添加新的子菜单到文档
                document.body.appendChild(newSubMenu);
                
                console.log('创建并显示了新的子菜单');
            });
        } else {
            menuItem.dataset.action = item.action;
            if (item.url) {
                menuItem.dataset.url = item.url;
            }
        }
        
        menu.appendChild(menuItem);
    });

    // 创建模态框容器
    const modal = document.createElement('div');
    modal.id = 'vm-query-modal';
    modal.style.display = 'none';
    
    // 创建关闭按钮
    const closeBtn = document.createElement('div');
    closeBtn.className = 'modal-close';
    closeBtn.innerHTML = '×';
    modal.appendChild(closeBtn);

    // 创建iframe
    const iframe = document.createElement('iframe');
    iframe.src = chrome.runtime.getURL('tools/popup.html');
    modal.appendChild(iframe);

    // 添加元素到页面
    document.body.appendChild(button);
    document.body.appendChild(menu);
    document.body.appendChild(modal);

    // 添加拖动功能
    let isDragging = false;
    let startY = 0;
    let scrollTop = 0;
    let currentTop = 0;

    const dragStart = (e) => {
        // 只响应鼠标左键
        if (e.button !== 0) return;
        
        isDragging = true;
        startY = e.clientY;
        currentTop = button.offsetTop;
        
        // 添加拖动时的样式
        button.classList.add('dragging');
        
        // 添加临时的全局事件监听
        document.addEventListener('mousemove', drag);
        document.addEventListener('mouseup', dragEnd);
        
        // 防止文本选择
        e.preventDefault();
    };

    const drag = (e) => {
        if (!isDragging) return;
        
        // 计算新位置
        const deltaY = e.clientY - startY;
        let newTop = currentTop + deltaY;
        
        // 限制范围
        const maxTop = window.innerHeight - button.offsetHeight;
        newTop = Math.max(0, Math.min(maxTop, newTop));
        
        // 更新位置
        updatePosition(newTop);
        
        // 当拖动到边缘时自动滚动
        const scrollThreshold = 50;
        if (e.clientY < scrollThreshold) {
            // 向上滚动
            window.scrollBy(0, -10);
        } else if (e.clientY > window.innerHeight - scrollThreshold) {
            // 向下滚动
            window.scrollBy(0, 10);
        }
    };

    const dragEnd = (e) => {
        isDragging = false;
        button.classList.remove('dragging');
        
        // 移除临时的全局事件监听
        document.removeEventListener('mousemove', drag);
        document.removeEventListener('mouseup', dragEnd);
        
        // 保存最终位置
        chrome.storage.local.set({
            buttonPosition: button.offsetTop
        });
    };

    const updatePosition = (top) => {
        // 使用 transform 来优化性能
        const translate = `translateY(0)`;
        
        button.style.top = `${top}px`;
        button.style.transform = translate;
        
        menu.style.top = `${top}px`;
        menu.style.transform = translate;
        
        if (modal) {
            modal.style.top = `${top}px`;
            modal.style.transform = translate;
        }
    };

    // 从 storage 恢复位置
    chrome.storage.local.get('buttonPosition', (data) => {
        if (data.buttonPosition !== undefined) {
            const savedPosition = data.buttonPosition;
            // 确保恢复的位置在当前窗口范围内
            const maxTop = window.innerHeight - button.offsetHeight;
            const validPosition = Math.max(0, Math.min(maxTop, savedPosition));
            updatePosition(validPosition);
        }
    });

    // 只在按钮上添加 mousedown 事件
    button.addEventListener('mousedown', dragStart);

    // 优化窗口大小改变时的处理
    let resizeTimeout;
    window.addEventListener('resize', () => {
        // 使用防抖来优化性能
        clearTimeout(resizeTimeout);
        resizeTimeout = setTimeout(() => {
            const maxTop = window.innerHeight - button.offsetHeight;
            const currentTop = button.offsetTop;
            const newTop = Math.max(0, Math.min(maxTop, currentTop));
            updatePosition(newTop);
        }, 100);
    });

    // 事件处理
    let menuTimeout;

    button.addEventListener('mouseenter', () => {
        if (!isDragging) {
            clearTimeout(menuTimeout);
            button.style.right = '0';
            menu.classList.add('show');
        }
    });

    menu.addEventListener('mouseenter', () => {
        if (!isDragging) {
            clearTimeout(menuTimeout);
            button.style.right = '0';
            menu.classList.add('show');
        }
    });

    const hideMenu = () => {
        if (!isDragging) {
            menuTimeout = setTimeout(() => {
                button.style.right = '-35px';
                menu.classList.remove('show');
            }, 300);
        }
    };

    button.addEventListener('mouseleave', hideMenu);
    menu.addEventListener('mouseleave', hideMenu);

    // 窗口大小改变时确保按钮在可视区域内
    window.addEventListener('resize', () => {
        const maxY = window.innerHeight - button.offsetHeight;
        // 使用 button.offsetTop 替代 yOffset
        const currentTop = button.offsetTop;
        const newTop = Math.max(0, Math.min(maxY, currentTop));
        updatePosition(newTop);
    });

    // 菜单项点击事件 - 简化为只处理子菜单的显示/隐藏
    menu.addEventListener('click', (e) => {
        if (isDragging) {
            e.preventDefault();
            return;
        }

        const menuItem = e.target.closest('.floating-menu-item');
        if (!menuItem) return;
        
        // 如果点击的是一级菜单项，事件已经在上面的代码中处理
        // 这里不需要额外的处理
    });

    closeBtn.addEventListener('click', (e) => {
        e.stopPropagation();
        modal.style.display = 'none';
    });

    document.addEventListener('click', (e) => {
        if (!modal.contains(e.target) && !menu.contains(e.target) && e.target !== button) {
            modal.style.display = 'none';
            menu.classList.remove('show');
        }
    });

    // 更新子菜单样式，使用更简单的定位方式
    const style = document.createElement('style');
    style.textContent = `
        .floating-menu-item.has-submenu {
            position: relative;
            cursor: pointer;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .submenu-indicator {
            margin-left: 8px;
            font-size: 10px;
            transform: rotate(180deg); /* 箭头指向左侧 */
        }
        
        .floating-submenu {
            display: none;
            position: fixed; /* 使用fixed定位，避免相对定位问题 */
            background: white;
            border-radius: 6px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.2);
            z-index: 2147483647;
            min-width: 150px;
            padding: 5px 0;
            border: 1px solid #e0e0e0;
            /* 添加明显的调试样式 */
            background-color: #f0f0f0;
            border: 2px solid #ff0000;
        }
        
        .floating-submenu .floating-menu-item {
            padding: 8px 15px;
            white-space: nowrap;
        }
    `;
    document.head.appendChild(style);
}

// 确保DOM加载完成后执行
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', createElements);
} else {
    createElements();
}

// 为了处理某些单页应用的情况，添加URL变化监听
let lastUrl = location.href;
new MutationObserver(() => {
    const url = location.href;
    if (url !== lastUrl) {
        lastUrl = url;
        createElements();
    }
}).observe(document, { subtree: true, childList: true });

// 点击文档其他区域时关闭子菜单
document.addEventListener('click', (e) => {
    // 如果点击的不是菜单项或其子元素
    if (!e.target.closest('.floating-menu-item')) {
        // 隐藏所有子菜单
        document.querySelectorAll('.floating-submenu').forEach(sm => {
            sm.style.display = 'none';
        });
    }
    
    // 获取模态框元素
    const modal = document.getElementById('vm-query-modal');
    const menu = document.querySelector('.floating-menu');
    const button = document.getElementById('vm-query-button');
    
    // 原有的关闭模态框的逻辑
    if (modal && !modal.contains(e.target) && !menu.contains(e.target) && e.target !== button) {
        modal.style.display = 'none';
        if (menu) menu.classList.remove('show');
    }
});

// 添加调试代码，帮助确定子菜单显示问题
document.addEventListener('DOMContentLoaded', () => {
    // 延迟执行，确保菜单已创建
    setTimeout(() => {
        const hasSubmenuItems = document.querySelectorAll('.floating-menu-item.has-submenu');
        console.log(`找到 ${hasSubmenuItems.length} 个带子菜单的菜单项`);
        
        hasSubmenuItems.forEach((item, index) => {
            const subMenu = item.querySelector('.floating-submenu');
            if (subMenu) {
                console.log(`菜单项 ${index + 1} 有子菜单，包含 ${subMenu.children.length} 个子项`);
                
                // 添加点击事件监听器
                item.addEventListener('click', (e) => {
                    console.log(`点击了菜单项: ${item.textContent.trim()}`);
                    console.log(`子菜单当前显示状态: ${subMenu.style.display}`);
                    
                    // 切换子菜单显示状态
                    if (subMenu.style.display === 'block') {
                        subMenu.style.display = 'none';
                        console.log('隐藏子菜单');
                    } else {
                        subMenu.style.display = 'block';
                        console.log('显示子菜单');
                        
                        // 输出子菜单位置信息
                        const rect = subMenu.getBoundingClientRect();
                        console.log(`子菜单位置: left=${rect.left}, top=${rect.top}, width=${rect.width}, height=${rect.height}`);
                    }
                    
                    e.stopPropagation();
                });
            }
        });
    }, 1000);
});